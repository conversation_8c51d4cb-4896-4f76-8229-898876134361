#!/usr/bin/env python3
"""
Test script for MONDO standardizer
"""

import json
import tempfile
import os
from mondo_standardizer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_standardizer():
    """Test the MONDO standardizer with sample data."""
    
    # Create test data
    test_data = [
        {
            "adc": {"adc_name": "Test-ADC-1"},
            "model": {
                "citations": ["Original citation 1"],
                "cancer_type": "Colorectal cancer",  # This should get MONDO:0005575
                "cancer_subtype": "Adenocarcinoma",  # This should get MONDO:0004970
                "investigative": True
            }
        },
        {
            "adc": {"adc_name": "Test-ADC-2"},
            "model": {
                "citations": ["Original citation 2"],
                "cancer_type": "Non-small cell lung cancer (NSCLC)",  # This should get MONDO:0005233
                "cancer_subtype": None,
                "investigative": True
            }
        },
        {
            "adc": {"adc_name": "Test-ADC-3"},
            "model": {
                "citations": ["Original citation 3"],
                "cancer_type": "Not applicable",  # This should stay as "not applicable"
                "cancer_subtype": None,
                "investigative": True
            }
        },
        {
            "adc": {"adc_name": "Test-ADC-4"},
            "model": {
                "citations": ["Original citation 4"],
                "cancer_type": "Breast cancer (mouse model)",  # This should get MONDO:0007254
                "cancer_subtype": "Mammary carcinoma",  # This should get MONDO:0002067
                "investigative": True
            }
        }
    ]
    
    # Initialize standardizer
    standardizer = MondoStandardizer()
    
    # Test standardization
    standardized_data = standardizer.standardize_results(test_data)
    
    # Verify results
    print("Testing MONDO Standardizer...")
    print("=" * 50)
    
    for i, (original, standardized) in enumerate(zip(test_data, standardized_data)):
        print(f"\nTest Case {i+1}:")
        print(f"Original cancer_type: {original['model']['cancer_type']}")
        print(f"Standardized cancer_type: {standardized['model']['cancer_type']}")
        
        print(f"Original cancer_subtype: {original['model']['cancer_subtype']}")
        print(f"Standardized cancer_subtype: {standardized['model']['cancer_subtype']}")
        
        # Check if citations were added
        original_citations = len(original['model']['citations'])
        standardized_citations = len(standardized['model']['citations'])
        if standardized_citations > original_citations:
            print("✓ MONDO citations added")
            for citation in standardized['model']['citations'][original_citations:]:
                print(f"  - {citation}")
        else:
            print("- No MONDO citations added (expected for 'Not applicable')")
    
    print("\n" + "=" * 50)
    print("Test completed successfully!")
    
    return standardized_data


def test_file_processing():
    """Test file processing functionality."""
    print("\nTesting file processing...")
    
    # Create test data
    test_data = [
        {
            "adc": {"adc_name": "File-Test-ADC"},
            "model": {
                "citations": ["File test citation"],
                "cancer_type": "Pancreatic cancer",
                "cancer_subtype": "Carcinoma",
                "investigative": True
            }
        }
    ]
    
    # Create temporary files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as input_file:
        json.dump(test_data, input_file, indent=2)
        input_filename = input_file.name
    
    output_filename = input_filename.replace('.json', '_standardized.json')
    
    try:
        # Test file processing
        standardizer = MondoStandardizer()
        standardizer.process_file(input_filename, output_filename)
        
        # Read and verify output
        with open(output_filename, 'r') as f:
            result_data = json.load(f)
        
        print(f"✓ File processing successful")
        print(f"Input file: {input_filename}")
        print(f"Output file: {output_filename}")
        print(f"Standardized cancer_type: {result_data[0]['model']['cancer_type']}")
        print(f"Standardized cancer_subtype: {result_data[0]['model']['cancer_subtype']}")
        
        # Check for MONDO citations
        citations = result_data[0]['model']['citations']
        mondo_citations = [c for c in citations if 'MONDO ontology' in c]
        print(f"MONDO citations added: {len(mondo_citations)}")
        for citation in mondo_citations:
            print(f"  - {citation}")
        
    finally:
        # Clean up temporary files
        if os.path.exists(input_filename):
            os.unlink(input_filename)
        if os.path.exists(output_filename):
            os.unlink(output_filename)


if __name__ == "__main__":
    # Run tests
    test_standardizer()
    test_file_processing()
    print("\nAll tests completed!")
