#!/usr/bin/env python3
"""
Comprehensive test suite for the Dynamic MONDO Cancer Term Standardizer
"""

import json
import tempfile
import os
import time
from dynamic_mondo_standardizer import DynamicMondoStandardizer, CancerTermMatcher, MondoAPIClient


def test_cancer_term_matcher():
    """Test the CancerTermMatcher functionality."""
    print("Testing CancerTermMatcher...")
    print("=" * 50)
    
    matcher = CancerTermMatcher()
    
    # Test normalization
    test_cases = [
        ("Breast Cancer", "breast cancer"),
        ("Non-small cell lung cancer (NSCLC)", "non-small cell lung cancer"),
        ("NSCLC", "non-small cell lung cancer"),  # Abbreviation expansion
        ("Human colorectal adenocarcinoma cell line", "colorectal adenocarcinoma"),
        ("Mouse mammary carcinoma model", "mammary carcinoma")
    ]
    
    print("1. Testing term normalization:")
    for original, expected in test_cases:
        normalized = matcher.normalize_term(original)
        status = "✓" if expected in normalized else "✗"
        print(f"  {status} '{original}' → '{normalized}'")
    
    # Test cancer term detection
    print("\n2. Testing cancer term detection:")
    cancer_terms = ["breast cancer", "lung adenocarcinoma", "glioblastoma", "melanoma"]
    non_cancer_terms = ["fibroblast", "protein assay", "not applicable", "mouse model"]
    
    for term in cancer_terms:
        is_cancer = matcher.is_cancer_term(term)
        status = "✓" if is_cancer else "✗"
        print(f"  {status} '{term}' → Cancer: {is_cancer}")
    
    for term in non_cancer_terms:
        is_cancer = matcher.is_cancer_term(term)
        status = "✓" if not is_cancer else "✗"
        print(f"  {status} '{term}' → Cancer: {is_cancer}")
    
    # Test similarity calculation
    print("\n3. Testing similarity calculation:")
    similarity_tests = [
        ("breast cancer", "mammary cancer", 0.5),
        ("lung cancer", "pulmonary cancer", 0.5),
        ("nsclc", "non-small cell lung cancer", 0.3)
    ]
    
    for term1, term2, min_expected in similarity_tests:
        similarity = matcher.calculate_similarity(term1, term2)
        status = "✓" if similarity >= min_expected else "✗"
        print(f"  {status} '{term1}' vs '{term2}' → {similarity:.3f}")


def test_mondo_api_client():
    """Test the MondoAPIClient functionality."""
    print("\nTesting MondoAPIClient...")
    print("=" * 50)
    
    client = MondoAPIClient()
    
    # Test search functionality
    print("1. Testing MONDO search:")
    test_searches = ["breast cancer", "lung adenocarcinoma", "melanoma"]
    
    for term in test_searches:
        print(f"  Searching for '{term}'...")
        results = client.search_terms(term)
        
        if results:
            print(f"    ✓ Found {len(results)} results")
            # Show first result
            first_result = results[0]
            label = first_result.get('label', first_result.get('name', 'Unknown'))
            mondo_id = first_result.get('obo_id', first_result.get('id', 'Unknown'))
            print(f"    Top result: {label} ({mondo_id})")
        else:
            print(f"    ✗ No results found")
        
        time.sleep(0.2)  # Rate limiting
    
    # Test term details
    print("\n2. Testing term details retrieval:")
    test_ids = ["MONDO:0007254", "MONDO:0005233"]  # breast cancer, NSCLC
    
    for mondo_id in test_ids:
        print(f"  Getting details for {mondo_id}...")
        details = client.get_term_details(mondo_id)
        
        if details:
            label = details.get('label', details.get('name', 'Unknown'))
            print(f"    ✓ Found: {label}")
        else:
            print(f"    ✗ No details found")
        
        time.sleep(0.2)  # Rate limiting


def test_dynamic_standardizer():
    """Test the main DynamicMondoStandardizer functionality."""
    print("\nTesting DynamicMondoStandardizer...")
    print("=" * 50)
    
    # Initialize with lower confidence for testing
    standardizer = DynamicMondoStandardizer(confidence_threshold=0.6, max_workers=2)
    
    # Test individual term standardization
    print("1. Testing individual term standardization:")
    test_terms = [
        "Breast cancer",
        "NSCLC", 
        "Colorectal adenocarcinoma",
        "Pancreatic cancer",
        "Not applicable"
    ]
    
    for term in test_terms:
        print(f"  Standardizing '{term}'...")
        result = standardizer.standardize_cancer_term(term)
        
        if result:
            print(f"    ✓ {result.term} ({result.mondo_id})")
            print(f"      Confidence: {result.confidence:.3f}, Type: {result.match_type}")
        else:
            print(f"    ✗ No match found")
        
        time.sleep(0.3)  # Rate limiting
    
    # Test entry standardization
    print("\n2. Testing entry standardization:")
    test_entry = {
        "adc": {"adc_name": "Test-ADC"},
        "model": {
            "citations": ["Test citation for breast cancer research"],
            "cancer_type": "Breast cancer",
            "cancer_subtype": "Adenocarcinoma",
            "investigative": True
        }
    }
    
    print("  Original entry:")
    print(f"    Cancer type: {test_entry['model']['cancer_type']}")
    print(f"    Cancer subtype: {test_entry['model']['cancer_subtype']}")
    
    standardized_entry = standardizer.standardize_entry(test_entry)
    
    print("  Standardized entry:")
    print(f"    Cancer type: {standardized_entry['model']['cancer_type']}")
    print(f"    Cancer subtype: {standardized_entry['model']['cancer_subtype']}")
    
    # Check if citations were added
    original_citations = len(test_entry['model']['citations'])
    standardized_citations = len(standardized_entry['model']['citations'])
    
    if standardized_citations > original_citations:
        print("    ✓ MONDO citations added:")
        for citation in standardized_entry['model']['citations'][original_citations:]:
            print(f"      - {citation}")
    else:
        print("    - No MONDO citations added")


def test_file_processing():
    """Test file processing functionality."""
    print("\nTesting file processing...")
    print("=" * 50)
    
    # Create test data
    test_data = [
        {
            "adc": {"adc_name": "Test-ADC-1"},
            "model": {
                "citations": ["Research on lung cancer"],
                "cancer_type": "Non-small cell lung cancer",
                "cancer_subtype": "Adenocarcinoma",
                "investigative": True
            }
        },
        {
            "adc": {"adc_name": "Test-ADC-2"},
            "model": {
                "citations": ["Breast cancer study"],
                "cancer_type": "Breast cancer",
                "cancer_subtype": None,
                "investigative": True
            }
        },
        {
            "adc": {"adc_name": "Test-ADC-3"},
            "model": {
                "citations": ["Control study"],
                "cancer_type": "Not applicable",
                "cancer_subtype": None,
                "investigative": True
            }
        }
    ]
    
    # Create temporary files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as input_file:
        json.dump(test_data, input_file, indent=2)
        input_filename = input_file.name
    
    output_filename = input_filename.replace('.json', '_standardized.json')
    
    try:
        # Test file processing
        standardizer = DynamicMondoStandardizer(confidence_threshold=0.6)
        standardizer.process_file(input_filename, output_filename)
        
        # Read and verify output
        with open(output_filename, 'r') as f:
            result_data = json.load(f)
        
        print(f"✓ File processing successful")
        print(f"  Processed {len(result_data)} entries")
        
        # Check standardization results
        for i, entry in enumerate(result_data):
            if "model" in entry and entry["model"].get("cancer_type"):
                original_type = test_data[i]["model"]["cancer_type"]
                standardized_type = entry["model"]["cancer_type"]
                print(f"  Entry {i+1}: '{original_type}' → '{standardized_type}'")
        
        # Generate and display report
        report = standardizer.get_standardization_report()
        print("\n  Standardization Report:")
        for key, value in report.items():
            if key in ['total_cached_terms', 'successful_matches', 'api_requests_made']:
                print(f"    {key}: {value}")
        
    finally:
        # Clean up temporary files
        if os.path.exists(input_filename):
            os.unlink(input_filename)
        if os.path.exists(output_filename):
            os.unlink(output_filename)


def test_performance():
    """Test performance with larger dataset."""
    print("\nTesting performance...")
    print("=" * 50)
    
    # Create larger test dataset
    cancer_types = [
        "Breast cancer", "Lung cancer", "NSCLC", "Colorectal cancer",
        "Pancreatic cancer", "Liver cancer", "Kidney cancer", "Brain cancer",
        "Melanoma", "Lymphoma", "Leukemia", "Sarcoma"
    ]
    
    test_data = []
    for i in range(20):  # Create 20 entries
        cancer_type = cancer_types[i % len(cancer_types)]
        test_data.append({
            "adc": {"adc_name": f"Test-ADC-{i+1}"},
            "model": {
                "citations": [f"Research on {cancer_type.lower()}"],
                "cancer_type": cancer_type,
                "cancer_subtype": "Adenocarcinoma" if i % 3 == 0 else None,
                "investigative": True
            }
        })
    
    # Test processing time
    standardizer = DynamicMondoStandardizer(confidence_threshold=0.6, max_workers=2)
    
    start_time = time.time()
    standardized_data = standardizer.standardize_results(test_data)
    end_time = time.time()
    
    processing_time = end_time - start_time
    
    print(f"✓ Processed {len(test_data)} entries in {processing_time:.2f} seconds")
    print(f"  Average time per entry: {processing_time/len(test_data):.3f} seconds")
    
    # Check cache effectiveness
    report = standardizer.get_standardization_report()
    cache_hit_ratio = report['successful_matches'] / max(report['total_cached_terms'], 1)
    print(f"  Cache hit ratio: {cache_hit_ratio:.2f}")
    print(f"  API requests made: {report['api_requests_made']}")


def main():
    """Run all tests."""
    print("Dynamic MONDO Standardizer Test Suite")
    print("=" * 60)
    
    try:
        test_cancer_term_matcher()
        test_mondo_api_client()
        test_dynamic_standardizer()
        test_file_processing()
        test_performance()
        
        print("\n" + "=" * 60)
        print("All tests completed successfully!")
        print("Note: Some tests may fail due to API availability or network issues.")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
