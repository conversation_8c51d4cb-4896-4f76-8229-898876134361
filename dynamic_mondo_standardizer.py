#!/usr/bin/env python3
"""
Dynamic MONDO Cancer Term Standardizer

A scalable, production-ready cancer term standardizer that dynamically queries
the MONDO ontology without hardcoded mappings. Designed for processing 1000+
research papers with diverse cancer terminology.

Features:
- Real-time MONDO ontology API integration
- Fuzzy matching with confidence scoring
- Synonym and abbreviation handling
- Batch processing optimization
- No hardcoded mappings
- Comprehensive error handling

Usage:
    python dynamic_mondo_standardizer.py input_results.json output_results.json
"""

import json
import sys
import re
import time
import logging
import requests
import urllib.parse
from typing import Dict, Any, Optional, Tuple, List, Set
from dataclasses import dataclass
from functools import lru_cache
from difflib import SequenceMatcher
import concurrent.futures
from threading import Lock
import copy

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class MatchResult:
    """Represents a cancer term match result."""
    mondo_id: str
    term: str
    confidence: float
    match_type: str  # 'exact', 'synonym', 'fuzzy', 'abbreviation'
    definition: Optional[str] = None


class MondoAPIClient:
    """Client for interacting with MONDO ontology APIs."""
    
    def __init__(self):
        self.base_urls = [
            "https://www.ebi.ac.uk/ols4/api",
            "https://ontology.monarchinitiative.org/api",
            "https://api.monarchinitiative.org/api"
        ]
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DynamicMondoStandardizer/1.0',
            'Accept': 'application/json'
        })
        self.request_lock = Lock()
        self.request_count = 0
        self.last_request_time = 0
        
    def _rate_limit(self):
        """Implement rate limiting to avoid overwhelming APIs."""
        with self.request_lock:
            current_time = time.time()
            if current_time - self.last_request_time < 0.1:  # 100ms between requests
                time.sleep(0.1)
            self.last_request_time = time.time()
            self.request_count += 1
    
    def _make_request(self, url: str, params: Dict = None) -> Optional[Dict]:
        """Make a rate-limited API request with fallback URLs."""
        self._rate_limit()
        
        for base_url in self.base_urls:
            try:
                full_url = f"{base_url.rstrip('/')}/{url.lstrip('/')}"
                response = self.session.get(full_url, params=params, timeout=10)
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # Rate limited
                    logger.warning(f"Rate limited by {base_url}, waiting...")
                    time.sleep(1)
                    continue
                else:
                    logger.warning(f"API request failed: {response.status_code} from {base_url}")
                    continue
                    
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request failed for {base_url}: {e}")
                continue
        
        return None
    
    @lru_cache(maxsize=1000)
    def search_terms(self, query: str, ontology: str = "mondo") -> List[Dict]:
        """Search for terms in MONDO ontology."""
        params = {
            'q': query,
            'ontology': ontology,
            'type': 'class',
            'rows': 20,
            'exact': 'false'
        }
        
        # Try OLS4 API first
        result = self._make_request("search", params)
        if result and 'response' in result and 'docs' in result['response']:
            return result['response']['docs']
        
        # Fallback to alternative search endpoints
        for endpoint in ["terms", "entities"]:
            result = self._make_request(f"{endpoint}/search", params)
            if result:
                return result.get('results', result.get('docs', []))
        
        return []
    
    @lru_cache(maxsize=500)
    def get_term_details(self, mondo_id: str) -> Optional[Dict]:
        """Get detailed information about a MONDO term."""
        # Clean the MONDO ID
        if not mondo_id.startswith('MONDO:'):
            mondo_id = f"MONDO:{mondo_id}"
        
        # Try different API endpoints
        endpoints = [
            f"ontologies/mondo/terms/{urllib.parse.quote(mondo_id, safe='')}",
            f"terms/{urllib.parse.quote(mondo_id, safe='')}",
            f"entities/{mondo_id}"
        ]
        
        for endpoint in endpoints:
            result = self._make_request(endpoint)
            if result:
                return result
        
        return None


class CancerTermMatcher:
    """Advanced cancer term matching with fuzzy logic and confidence scoring."""
    
    def __init__(self):
        self.cancer_keywords = {
            'cancer', 'carcinoma', 'adenocarcinoma', 'sarcoma', 'lymphoma', 
            'leukemia', 'melanoma', 'glioma', 'blastoma', 'tumor', 'tumour',
            'neoplasm', 'malignancy', 'oncology'
        }
        
        # Common abbreviations and their expansions
        self.abbreviations = {
            'nsclc': 'non-small cell lung cancer',
            'sclc': 'small cell lung cancer',
            'crc': 'colorectal cancer',
            'hcc': 'hepatocellular carcinoma',
            'rcc': 'renal cell carcinoma',
            'pdac': 'pancreatic ductal adenocarcinoma',
            'gbm': 'glioblastoma multiforme',
            'aml': 'acute myeloid leukemia',
            'all': 'acute lymphoblastic leukemia',
            'cml': 'chronic myeloid leukemia',
            'cll': 'chronic lymphocytic leukemia',
            'dlbcl': 'diffuse large b-cell lymphoma',
            'tnbc': 'triple negative breast cancer'
        }
        
        # Synonyms and alternative names
        self.synonyms = {
            'lung cancer': ['pulmonary cancer', 'bronchogenic carcinoma'],
            'breast cancer': ['mammary cancer', 'mammary carcinoma'],
            'colon cancer': ['colorectal cancer', 'bowel cancer'],
            'liver cancer': ['hepatic cancer', 'hepatocellular carcinoma'],
            'kidney cancer': ['renal cancer', 'renal cell carcinoma'],
            'brain cancer': ['brain tumor', 'cerebral cancer', 'glioma'],
            'blood cancer': ['hematologic malignancy', 'leukemia', 'lymphoma']
        }
    
    def normalize_term(self, term: str) -> str:
        """Normalize a cancer term for matching."""
        if not term:
            return ""
        
        # Convert to lowercase and strip
        normalized = term.lower().strip()
        
        # Remove common prefixes/suffixes
        normalized = re.sub(r'\b(human|mouse|murine|cell line|model)\b', '', normalized)
        normalized = re.sub(r'\(.*?\)', '', normalized)  # Remove parenthetical content
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        # Expand abbreviations
        if normalized in self.abbreviations:
            normalized = self.abbreviations[normalized]
        
        return normalized
    
    def is_cancer_term(self, term: str) -> bool:
        """Check if a term is likely a cancer-related term."""
        normalized = self.normalize_term(term)
        
        # Check for cancer keywords
        return any(keyword in normalized for keyword in self.cancer_keywords)
    
    def calculate_similarity(self, term1: str, term2: str) -> float:
        """Calculate similarity between two terms."""
        return SequenceMatcher(None, term1.lower(), term2.lower()).ratio()
    
    def find_best_matches(self, query_term: str, candidate_terms: List[Dict]) -> List[MatchResult]:
        """Find best matches from candidate terms with confidence scoring."""
        if not candidate_terms:
            return []
        
        normalized_query = self.normalize_term(query_term)
        matches = []
        
        for candidate in candidate_terms:
            # Extract term information
            term_label = candidate.get('label', candidate.get('name', ''))
            mondo_id = candidate.get('obo_id', candidate.get('id', ''))
            definition = candidate.get('description', candidate.get('definition', ''))
            
            if not term_label or not mondo_id:
                continue
            
            # Skip non-cancer terms
            if not self.is_cancer_term(term_label):
                continue
            
            normalized_candidate = self.normalize_term(term_label)
            
            # Calculate confidence and match type
            confidence = 0.0
            match_type = 'fuzzy'
            
            # Exact match
            if normalized_query == normalized_candidate:
                confidence = 1.0
                match_type = 'exact'
            
            # Synonym match
            elif self._check_synonym_match(normalized_query, normalized_candidate):
                confidence = 0.95
                match_type = 'synonym'
            
            # Abbreviation match
            elif normalized_query in self.abbreviations and self.abbreviations[normalized_query] == normalized_candidate:
                confidence = 0.9
                match_type = 'abbreviation'
            
            # Fuzzy match
            else:
                similarity = self.calculate_similarity(normalized_query, normalized_candidate)
                if similarity >= 0.7:  # Minimum threshold for fuzzy matching
                    confidence = similarity * 0.8  # Scale down fuzzy matches
                    match_type = 'fuzzy'
            
            if confidence > 0:
                matches.append(MatchResult(
                    mondo_id=mondo_id,
                    term=term_label,
                    confidence=confidence,
                    match_type=match_type,
                    definition=definition
                ))
        
        # Sort by confidence (descending)
        matches.sort(key=lambda x: x.confidence, reverse=True)
        return matches[:10]  # Return top 10 matches
    
    def _check_synonym_match(self, query: str, candidate: str) -> bool:
        """Check if terms are synonyms."""
        for canonical, synonyms in self.synonyms.items():
            if (query == canonical and candidate in synonyms) or \
               (candidate == canonical and query in synonyms) or \
               (query in synonyms and candidate in synonyms):
                return True
        return False


class DynamicMondoStandardizer:
    """
    Dynamic MONDO cancer term standardizer without hardcoded mappings.
    """
    
    def __init__(self, confidence_threshold: float = 0.7, max_workers: int = 4):
        """
        Initialize the dynamic standardizer.
        
        Args:
            confidence_threshold: Minimum confidence score for accepting matches
            max_workers: Number of worker threads for parallel processing
        """
        self.api_client = MondoAPIClient()
        self.matcher = CancerTermMatcher()
        self.confidence_threshold = confidence_threshold
        self.max_workers = max_workers
        
        # Cache for processed terms to avoid redundant API calls
        self._term_cache = {}
        self._cache_lock = Lock()
        
        logger.info(f"Initialized DynamicMondoStandardizer with confidence threshold: {confidence_threshold}")
    
    def standardize_cancer_term(self, term: str, context: Optional[str] = None) -> Optional[MatchResult]:
        """
        Standardize a single cancer term using MONDO ontology.
        
        Args:
            term: Cancer term to standardize
            context: Optional context to improve matching
            
        Returns:
            Best match result or None if no suitable match found
        """
        if not term or term.lower().strip() in ['not applicable', 'n/a', 'none', 'null']:
            return None
        
        # Check cache first
        cache_key = f"{term.lower()}:{context or ''}"
        with self._cache_lock:
            if cache_key in self._term_cache:
                return self._term_cache[cache_key]
        
        # Search MONDO ontology
        search_results = self.api_client.search_terms(term)
        
        # Find best matches
        matches = self.matcher.find_best_matches(term, search_results)
        
        # Select best match above confidence threshold
        best_match = None
        if matches and matches[0].confidence >= self.confidence_threshold:
            best_match = matches[0]
            
            # Enhance with detailed information
            details = self.api_client.get_term_details(best_match.mondo_id)
            if details and not best_match.definition:
                best_match.definition = details.get('description', details.get('definition'))
        
        # Cache result
        with self._cache_lock:
            self._term_cache[cache_key] = best_match
        
        return best_match
    
    def standardize_entry(self, entry: Dict[str, Any]) -> Dict[str, Any]:
        """
        Standardize cancer terms in a single JSON entry.
        
        Args:
            entry: Dictionary representing one entry
            
        Returns:
            Entry with standardized cancer terms and citations
        """
        # Create deep copy to avoid modifying original
        standardized_entry = copy.deepcopy(entry)
        
        # Process model section if it exists
        if "model" in entry and isinstance(entry["model"], dict):
            model = entry["model"]
            
            # Get context from citations
            context = None
            if "citations" in model and model["citations"]:
                context = " ".join(model["citations"][:2])  # Use first 2 citations
            
            # Standardize cancer_type
            if "cancer_type" in model and model["cancer_type"]:
                cancer_type_match = self.standardize_cancer_term(model["cancer_type"], context)
                if cancer_type_match:
                    standardized_entry["model"]["cancer_type"] = cancer_type_match.term
                    
                    # Add citation with confidence and MONDO ID
                    citations = standardized_entry["model"].get("citations", [])
                    citation = (f"Cancer type standardized to MONDO ontology: {cancer_type_match.mondo_id} "
                              f"({cancer_type_match.term}) [confidence: {cancer_type_match.confidence:.2f}, "
                              f"match_type: {cancer_type_match.match_type}]")
                    citations.append(citation)
                    standardized_entry["model"]["citations"] = citations
            
            # Standardize cancer_subtype
            if "cancer_subtype" in model and model["cancer_subtype"]:
                subtype_match = self.standardize_cancer_term(model["cancer_subtype"], context)
                if subtype_match:
                    standardized_entry["model"]["cancer_subtype"] = subtype_match.term
                    
                    # Add citation with confidence and MONDO ID
                    citations = standardized_entry["model"].get("citations", [])
                    citation = (f"Cancer subtype standardized to MONDO ontology: {subtype_match.mondo_id} "
                              f"({subtype_match.term}) [confidence: {subtype_match.confidence:.2f}, "
                              f"match_type: {subtype_match.match_type}]")
                    citations.append(citation)
                    standardized_entry["model"]["citations"] = citations
        
        return standardized_entry

    def standardize_results(self, results_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Standardize all entries in the results JSON with parallel processing.

        Args:
            results_data: List of result entries

        Returns:
            List of standardized entries
        """
        logger.info(f"Standardizing {len(results_data)} entries...")

        # Use parallel processing for large datasets
        if len(results_data) > 10 and self.max_workers > 1:
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                standardized_results = list(executor.map(self.standardize_entry, results_data))
        else:
            standardized_results = [self.standardize_entry(entry) for entry in results_data]

        logger.info(f"Standardization complete. Cache size: {len(self._term_cache)}")
        return standardized_results

    def process_file(self, input_file: str, output_file: str) -> None:
        """
        Process a JSON file and save standardized results.

        Args:
            input_file: Path to input JSON file
            output_file: Path to output JSON file
        """
        try:
            # Read input file
            logger.info(f"Reading input file: {input_file}")
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Standardize the data
            standardized_data = self.standardize_results(data)

            # Write output file
            logger.info(f"Writing output file: {output_file}")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(standardized_data, f, indent=2, ensure_ascii=False)

            # Report statistics
            total_entries = len(standardized_data)
            cancer_entries = sum(1 for entry in standardized_data
                               if "model" in entry and entry["model"].get("cancer_type"))

            logger.info(f"Successfully processed {total_entries} entries")
            logger.info(f"Cancer-related entries: {cancer_entries}")
            logger.info(f"API requests made: {self.api_client.request_count}")
            logger.info(f"Cache hits: {len(self._term_cache)}")

        except FileNotFoundError:
            logger.error(f"Input file '{input_file}' not found.")
            sys.exit(1)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in input file: {e}")
            sys.exit(1)
        except Exception as e:
            logger.error(f"Error processing file: {e}")
            sys.exit(1)

    def batch_process_directory(self, input_dir: str, output_dir: str, pattern: str = "*_results.json") -> None:
        """
        Process multiple JSON files in a directory.

        Args:
            input_dir: Directory containing input JSON files
            output_dir: Directory to save standardized results
            pattern: File pattern to match
        """
        import glob
        import os

        input_files = glob.glob(os.path.join(input_dir, pattern))

        if not input_files:
            logger.warning(f"No files matching pattern '{pattern}' found in {input_dir}")
            return

        logger.info(f"Found {len(input_files)} files to process")

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Process each file
        for input_file in input_files:
            filename = os.path.basename(input_file)
            output_file = os.path.join(output_dir, filename.replace('.json', '_standardized.json'))

            try:
                self.process_file(input_file, output_file)
            except Exception as e:
                logger.error(f"Failed to process {filename}: {e}")
                continue

        logger.info(f"Batch processing complete. Results saved to {output_dir}")

    def get_standardization_report(self) -> Dict[str, Any]:
        """
        Generate a report on standardization performance.

        Returns:
            Dictionary containing standardization statistics
        """
        cache_stats = {}
        confidence_distribution = {}
        match_type_distribution = {}

        for cached_result in self._term_cache.values():
            if cached_result:
                # Confidence distribution
                conf_bucket = f"{cached_result.confidence:.1f}"
                confidence_distribution[conf_bucket] = confidence_distribution.get(conf_bucket, 0) + 1

                # Match type distribution
                match_type_distribution[cached_result.match_type] = match_type_distribution.get(cached_result.match_type, 0) + 1

        return {
            "total_cached_terms": len(self._term_cache),
            "successful_matches": len([r for r in self._term_cache.values() if r is not None]),
            "failed_matches": len([r for r in self._term_cache.values() if r is None]),
            "api_requests_made": self.api_client.request_count,
            "confidence_distribution": confidence_distribution,
            "match_type_distribution": match_type_distribution,
            "confidence_threshold": self.confidence_threshold
        }


def main():
    """Main function to run the dynamic standardizer."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Dynamic MONDO Cancer Term Standardizer",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process single file
  python dynamic_mondo_standardizer.py input.json output.json

  # Process directory with custom confidence threshold
  python dynamic_mondo_standardizer.py --input-dir results/ --output-dir standardized/ --confidence 0.8

  # Generate report
  python dynamic_mondo_standardizer.py input.json output.json --report
        """
    )

    # File processing arguments
    parser.add_argument("input", nargs="?", help="Input JSON file")
    parser.add_argument("output", nargs="?", help="Output JSON file")

    # Directory processing arguments
    parser.add_argument("--input-dir", help="Input directory containing JSON files")
    parser.add_argument("--output-dir", help="Output directory for standardized files")
    parser.add_argument("--pattern", default="*_results.json", help="File pattern to match (default: *_results.json)")

    # Configuration arguments
    parser.add_argument("--confidence", type=float, default=0.7,
                       help="Minimum confidence threshold for matches (default: 0.7)")
    parser.add_argument("--workers", type=int, default=4,
                       help="Number of worker threads for parallel processing (default: 4)")
    parser.add_argument("--report", action="store_true",
                       help="Generate standardization report")

    args = parser.parse_args()

    # Validate arguments
    if not ((args.input and args.output) or (args.input_dir and args.output_dir)):
        parser.error("Either provide input/output files or input-dir/output-dir")

    # Initialize standardizer
    standardizer = DynamicMondoStandardizer(
        confidence_threshold=args.confidence,
        max_workers=args.workers
    )

    # Process files
    if args.input and args.output:
        # Single file processing
        standardizer.process_file(args.input, args.output)
    else:
        # Directory processing
        standardizer.batch_process_directory(args.input_dir, args.output_dir, args.pattern)

    # Generate report if requested
    if args.report:
        report = standardizer.get_standardization_report()
        print("\n" + "="*50)
        print("STANDARDIZATION REPORT")
        print("="*50)
        for key, value in report.items():
            print(f"{key}: {value}")


if __name__ == "__main__":
    main()
