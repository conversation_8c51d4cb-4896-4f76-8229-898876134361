import argparse
import pandas as pd
import asyncio
import os
import sqlite3
import logging
import json
import traceback
from typing import List, Optional, TypedDict, Annotated
from openai import AsyncAzureOpenAI
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from tqdm import tqdm
from dotenv import load_dotenv
from datetime import datetime
from pathlib import Path
from jinja2 import Template
from utils.extraction_pydantic_models import (
    AntibodyDrugConjugate, 
    ExperimentalModel,  
    EndpointName,
    get_endpoint_model
)
from langgraph.graph import StateGraph, START, END
from langgraph.types import Send
from pydantic import BaseModel, Field
from dataclasses import dataclass, field
import asyncio
from langgraph.graph import StateGraph, START, END
from rate_limiter import OpenAIRateLimiter
import operator
import logfire
from pydantic_ai.tools import RunContext
import sys
import time
import statistics

logfire.configure(scrubbing=False)
logfire.instrument_pydantic_ai()

logger = None

def setup_logging(logs_dir: str = "logs", logging_level: str = "DEBUG") -> logging.Logger:
    """Configure logging with both file and console handlers."""
    global logger
    
    # If logger is already initialized, return it
    if logger is not None:
        return logger
    
    # Create logs directory if it doesn't exist
    log_path = Path(logs_dir)
    log_path.mkdir(exist_ok=True)
    
    # Create a timestamped log file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_path / f"extraction_pipeline_parallel_{timestamp}.log"
    
    # Set log level
    log_level = getattr(logging, logging_level.upper())
    
    # Create formatters
    console_formatter = logging.Formatter('%(levelname)s - %(message)s')
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Setup handlers
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(console_formatter)
    
    # Use UTF-8 encoding for file handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)
    
    # Configure logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    logger.handlers = []  # Clear any existing handlers
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    logger.propagate = False
    
    logger.info(f"Logging initialized. Log file: {log_file}")
    return logger

load_dotenv()

@dataclass(frozen=True)
class SharedContext:
    """Immutable core data for a sample that can be safely shared across parallel nodes.
    
    Attributes:
        raw_text (str): The raw text being processed
        filename (str): The filename of the input file
    """
    raw_text: str 
    filename: str = ""
    
    def with_updates(self, **kwargs) -> "SharedContext":
        """Create a new instance with updated values."""
        return SharedContext(
            raw_text=kwargs.get('raw_text', self.raw_text),
            filename=kwargs.get('filename', self.filename)
        )

class MeasurementOpinionState(BaseModel):
    reasoning_for_opinion: str
    opinion: bool

client = AsyncAzureOpenAI(
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    azure_deployment='gpt-4.1',
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
)

# Initialize the OpenAI model with Azure provider
model = OpenAIModel(model_name='gpt-4.1', provider=OpenAIProvider(openai_client=client))
model_settings = {'temperature': 0.0, 'seed': 777}
# model_settings = {'reasoning_effort': 'high'}

# Initialize agents for each extraction step
adc_agent = Agent(
    model=model,
    system_prompt=open("prompts/adc_extraction_system_prompt.md").read(),
    result_type=List[AntibodyDrugConjugate],
    retries=3,
    result_retries=3,
    model_settings=model_settings,
    deps_type=SharedContext
)

model_agent = Agent(
    model=model,
    system_prompt=open("prompts/model_extraction_system_prompt.md").read(),
    # result_type=List[ExperimentalModel],
    retries=3,
    result_retries=3,
    model_settings=model_settings,
    deps_type=SharedContext
)

endpoint_agent = Agent(
    model=model,
    system_prompt=open("prompts/endpoint_extraction_system_prompt.md").read(),
    retries=20,
    result_retries=20,
    model_settings=model_settings,
    deps_type=SharedContext
)

check_endpoint_agent = Agent(
    model=model,
    system_prompt=open("prompts/endpoint_available_system_prompt.md").read(),
    result_type=EndpointName,
    retries=3,
    result_retries=3,
    model_settings=model_settings
)

measurement_opinion_agent = Agent(
    model=model,
    system_prompt=open("prompts/expert_opinion_system_prompt.md").read(),
    result_type=MeasurementOpinionState,
    retries=3,
    result_retries=3,
    model_settings=model_settings,
)

all_models_agent = Agent(
    model=model,    
    result_type=str,
    retries=3,
    system_prompt="You are an expert bioinformatician.",
    result_retries=3,
    model_settings=model_settings
)

# Initialize the rate limiter
rate_limiter = OpenAIRateLimiter(
    min_retry_delay=1.0,
    max_retry_delay=60.0,
    default_retry_delay=5.0,
    buffer_time=3.0,  # Add 1 second buffer to OpenAI's suggested wait time
    max_concurrent=5 # Adjust based on your OpenAI rate limits
)

# @rate_limiter.async_rate_limited
# async def run_all_models_agent(prompt):
#     return await all_models_agent.run(prompt)

@rate_limiter.async_rate_limited
async def run_adc_agent(prompt, deps: Optional[SharedContext] = None):
    return await adc_agent.run(prompt, deps=deps)

@rate_limiter.async_rate_limited
async def run_model_agent(prompt, result_type, deps: Optional[SharedContext] = None):
    return await model_agent.run(prompt, result_type=List[result_type], deps=deps)

@rate_limiter.async_rate_limited
async def run_check_endpoint_agent(prompt):
    return await check_endpoint_agent.run(prompt)

@rate_limiter.async_rate_limited
async def run_endpoint_agent_with_type(prompt, result_type, deps: Optional[SharedContext] = None):
    return await endpoint_agent.run(prompt, result_type=List[result_type], deps=deps)

@rate_limiter.async_rate_limited
async def run_measurement_opinion_agent(prompt):
    return await measurement_opinion_agent.run(prompt)

class ModelState(BaseModel):
    adc: AntibodyDrugConjugate
    model: ExperimentalModel

class AvailableEndpointState(BaseModel):
    adc: AntibodyDrugConjugate
    model: ExperimentalModel
    available_endpoint_name: str

class ModelsPerEndpoint(BaseModel):
    endpoint_name: str
    models: List[ExperimentalModel]
    adc: AntibodyDrugConjugate

class EndpointState(BaseModel):
    adc: AntibodyDrugConjugate
    endpoint_name: str
    endpoint_measurements: List 

class FinalEndpointState(BaseModel):
    adc_name: str
    endpoint_name: str
    endpoint_measurements: List
    reasoning: List[str]

class FinalState(TypedDict):
    shared_context: SharedContext 
    adcs: List[AntibodyDrugConjugate]
    all_models: str
    models: Annotated[List[ModelState], operator.add]
    available_endpoints: Annotated[List[AvailableEndpointState], operator.add]
    models_per_endpoint: List[ModelsPerEndpoint]
    endpoints: Annotated[List[EndpointState], operator.add]
    final_endpoints: Annotated[List[FinalEndpointState], operator.add]

@adc_agent.tool
def load_adc_names_into_memory(ctx: RunContext[SharedContext], reasoning_for_complete_names: str, adc_names: List[str]) -> str:
    """Load all identified ADC names into agent's memory"""
    logger.info(f"Loading {len(adc_names)} ADC names into memory")
    # This is a placeholder function; actual implementation may vary
    # Here we just log the names for demonstration purposes
    for name in adc_names:
        logger.debug(f"Loaded ADC name: {name}")
    
    return f"Loaded {len(adc_names)} ADC names into my memory."

@model_agent.tool
def load_model_names_into_memory(ctx: RunContext[SharedContext], adc_name: str, thought_process: str, model_names: List[str]) -> str:
    """Load all identified model names for a given ADC into agent's memory"""
    logger.info(f"Loading {len(model_names)} model names into memory for ADC: {adc_name}")
    # This is a placeholder function; actual implementation may vary
    # Here we just log the names for demonstration purposes
    for name in model_names:
        logger.debug(f"Loaded model name: {name}")

    return f"Loaded {len(model_names)} model names for ADC {adc_name} into my memory."

async def extract_adcs(state: FinalState) -> dict[str, List[AntibodyDrugConjugate]]:
    """Extract ADCs from the text"""
    logger.info("Extracting ADCs")
    user_prompt = Template(open("prompts/adc_extraction_user_prompt.md").read()).render(
        TEXT=state["shared_context"].raw_text
    )
    try:
        result = await run_adc_agent(user_prompt, deps=state["shared_context"])
        
        # Ensure adcs is always a list
        adcs = result.data if isinstance(result.data, list) else [result.data]
        logger.info(f"Extracted {len(adcs)} ADCs")
        
        return {"adcs": adcs}
        
    except Exception as e:
        logger.error(f"Error extracting ADCs: {str(e)}")
        # Return empty result on error
        return {"adcs": []}

async def extract_all_models_in_text(state: FinalState) -> dict[str, str]:
    """Extract all models mentioned in the text"""
    logger.info("Extracting all models from study as a free form text")
    user_prompt = Template(open("prompts/model_extraction_free_form_user_prompt.md").read()).render(
        TEXT=state["shared_context"].raw_text
    )
    
    try:
        all_models = await run_model_agent(user_prompt, result_type=str)

        logger.info(f"Extracted all models: {all_models.data}")
        return {"all_models": all_models.data}
        
    except Exception as e:
        logger.error(f"Error extracting all models: {str(e)}")
        return {"all_models": ""}


def continue_to_models(state: FinalState) -> List[Send]:
    # Determine ADC types to process based on filename (if available in shared context)
    filename = getattr(state["shared_context"], 'filename', '')
    
    if "review" in filename.lower():
        # Process both investigative and reference ADCs for review files
        adc_filter = lambda adc: adc.adc_type in ["Investigative", "Reference"]
        logger.info("Review file detected - processing both investigative and reference ADCs")
    elif "research" in filename.lower():
        # Process only investigative ADCs for research files
        adc_filter = lambda adc: adc.adc_type == "Investigative"
        logger.info("Research file detected - processing only investigative ADCs")
    else:
        # Default: process only investigative ADCs (existing behavior)
        adc_filter = lambda adc: adc.adc_type == "Investigative"
        logger.info("Standard processing - processing only investigative ADCs")
    
    return [
        Send(
            "extract_models",
            {
                "adc": adc,
                "raw_text": state["shared_context"],
                "all_models": state["all_models"]
            }
        )
        for adc in state["adcs"] if adc_filter(adc)
    ]


async def extract_models(state: ModelState) -> dict[str, List[ModelState]]:
    """Extract models for a specific ADC"""
    adc = state["adc"]
    raw_text = state["raw_text"].raw_text
    all_models = state["all_models"]
    
    logger.info(f"Extracting models for ADC: {adc.adc_name}")
    
    models_data = []

    try:
        user_prompt = Template(open("prompts/model_extraction_user_prompt.md").read()).render(
            TEXT=raw_text,
            ALL_MODELS=all_models,
            ADC=adc.adc_name
        )

        result = await run_model_agent(user_prompt, result_type=ExperimentalModel ,deps=state["raw_text"])
        
        # Ensure models is always a list
        models = result.data if isinstance(result.data, list) else [result.data]
        logger.info(f"Extracted {len(models)} models for ADC: {adc.adc_name}")
        
        # Create a new ADCWithModels with the model information
        for model in models:
            models_data.append(ModelState(adc=adc, model=model))
        return {"models": models_data}
    
    except Exception as e:
        logger.error(f"Error extracting models for ADC {adc.adc_name}: {str(e)}")
        return {"models": models_data}

def continue_to_check_available_endpoint(state: FinalState) -> List[Send]:
    """Create sends for endpoint extraction"""
    sends = []
    for model_state in state["models"]:
        if model_state.model.clinical_human_related and model_state.model.investigative:
            logger.info(f"Skipping clinical model: {model_state.model.model_name}")
            continue        
        sends.append(
            Send(
                "check_available_endpoint",
                {
                    "adc": model_state.adc,
                    "model": model_state.model,
                    "raw_text": state["shared_context"].raw_text
                }
            )
        )
    return sends
   
async def check_available_endpoint(state: AvailableEndpointState) -> dict[str, List[AvailableEndpointState]]:
    """Extract available endpoints for a specific ADC and model"""
    adc = state["adc"].adc_name
    antigen = state["adc"].antigen_name
    payload = state["adc"].payload_name 
    model = state["model"]
    raw_text = state["raw_text"]
    
    logger.info(f"Extracting available endpoints for ADC: {adc}, Model: {model.model_name}")
    user_prompt = Template(open("prompts/endpoint_available_user_prompt.md").read()).render(
        TEXT=raw_text,
        ADC=adc,
        ANTIGEN=antigen,
        PAYLOAD=payload,
        MODEL_NAME=model.model_name
    )
    list_of_available_endpoints = []
    try:
        result = await run_check_endpoint_agent(user_prompt)
        
        # Process the EndpointName object to extract only available endpoints
        endpoint_dict = result.data.model_dump()
        
        # Iterate through all fields in the EndpointName object
        for field_name, field_value in endpoint_dict.items():
                
            # Check if this endpoint is available
            if field_value is True:               
                # Add to available endpoints
                list_of_available_endpoints.append(
                    AvailableEndpointState(
                        adc=state['adc'],
                        model=model,
                        available_endpoint_name=field_name
                    )
                )
        
        logger.info(f"{len(list_of_available_endpoints)} endpoints are available for ADC: {adc}, Model: {model.model_name}")
        
        return {"available_endpoints": list_of_available_endpoints}
    except Exception as e:
        logger.error(f"Error extracting endpoint type for ADC {adc}, Model {model.model_name}: {str(e)}")
        logger.error(f"Exception details: {traceback.format_exc()}")
        return {"available_endpoints": list_of_available_endpoints}

async def create_models_per_endpoint(state: FinalState) -> dict[str, List[ModelsPerEndpoint]]:
    """Create a list of models per endpoint, grouped by ADC and endpoint"""
    # Group by (adc_name, endpoint_name) to preserve ADC-endpoint relationships
    endpoint_dict = {}
    for available_endpoint_state in state["available_endpoints"]:
        model = available_endpoint_state.model
        endpoint_name = available_endpoint_state.available_endpoint_name
        adc = available_endpoint_state.adc

        # Create a unique key combining ADC name and endpoint name
        key = (adc.adc_name, endpoint_name)
        if key not in endpoint_dict:
            endpoint_dict[key] = {"adc": adc, "models": []}
        endpoint_dict[key]["models"].append(model)

    models_per_endpoint = []
    for (adc_name, endpoint_name), data in endpoint_dict.items():
        models_per_endpoint.append(
            ModelsPerEndpoint(
                endpoint_name=endpoint_name,
                models=data["models"],
                adc=data["adc"]
            )
        )

    return {"models_per_endpoint": models_per_endpoint}

def continue_to_endpoints(state: FinalState) -> List[Send]:
    sends = []
    for models_per_endpoint in state["models_per_endpoint"]:
        sends.append(
            Send(
                "extract_endpoints",
                {
                    "adc": models_per_endpoint.adc,
                    "endpoint_name": models_per_endpoint.endpoint_name,
                    "models": models_per_endpoint.models,
                    "raw_text": state["shared_context"]
                }
            )
        )
    return sends

async def extract_endpoints(state: EndpointState) -> dict[str, List[EndpointState]]:
    """Extract endpoint measurements for a specific ADC, model, and endpoint name"""
    adc_name = state["adc"].adc_name
    antigen_name = state["adc"].antigen_name
    antibody_name = state["adc"].antibody_name
    payload = state["adc"].payload_name
    models = state["models"]
    endpoint_name = state["endpoint_name"]
    raw_text = state["raw_text"].raw_text

    # Create rich model context instead of just names
    model_names = [model.model_name for model in models]

    logger.info(f"Extracting endpoints for ADC: {adc_name}, Models: {model_names}, Endpoint: {endpoint_name}")

    user_prompt = Template(open("prompts/endpoint_extraction_user_prompt.md").read()).render(
        TEXT=raw_text,
        ADC=adc_name,
        PAYLOAD=payload,
        ANTIGEN=antigen_name,
        ANTIBODY=antibody_name,
        MODELS= model_names,
        ENDPOINT=endpoint_name
    )
    
    try:
        # Get the appropriate result type based on the endpoint name
        result_type = get_endpoint_model(endpoint_name)
        
        # Run the agent with the dynamic result type
        result = await run_endpoint_agent_with_type(user_prompt, result_type, state["raw_text"])
        
        # Get endpoint measurements from result
        endpoint_measurements = result.data
        logger.info(f"Successfully extracted endpoint measurement for ADC: {adc_name}, Models: {model_names}, Endpoint: {endpoint_name}")

        return {"endpoints": [EndpointState(adc=state['adc'], endpoint_name=endpoint_name, endpoint_measurements=endpoint_measurements)]}
    
    except Exception as e:
        logger.error(f"Error extracting endpoints for ADC {adc_name}, Models: {model_names}, Endpoint: {endpoint_name}: {str(e)}")
        logger.error(f"Exception details: {traceback.format_exc()}")
        return {"endpoints": []}
    
async def extract_final_endpoints(state: FinalState) -> dict[str, EndpointState]:
    """Discard unnecessary endpoint measurements based on expert opinion and save into final endpoints"""
    logger.info("Discarding unnecessary endpoint measurements")
    
    final_endpoints = []
    
    for endpoint_state in state["endpoints"]:
        adc_name = endpoint_state.adc.adc_name
        antigen_name = endpoint_state.adc.antigen_name
        endpoint_name = endpoint_state.endpoint_name

        final_endpoint = FinalEndpointState(
            adc_name=adc_name,  
            endpoint_name=endpoint_name,
            endpoint_measurements=[],
            reasoning=[]
        )

        for measurement in endpoint_state.endpoint_measurements:
            # Get expert opinion on this measurement
            user_prompt = Template(open("prompts/expert_opinion_user_prompt.md").read()).render(
                ADC=adc_name,
                ANTIGEN=antigen_name,
                ENDPOINT=endpoint_name,
                ENDPOINT_MEASUREMENT=measurement.model_dump_json()
            )
            
            try:
                result = await run_measurement_opinion_agent(user_prompt)
                
                measurement.expert_opinion = result.data.opinion
                final_endpoint.endpoint_measurements.append(measurement)
                final_endpoint.reasoning.append(result.data.reasoning_for_opinion)
                
                logger.info(f"Measurement for ADC {adc_name}, Endpoint {endpoint_name} is valid and will be saved.")
            except Exception as e:
                logger.error(f"Error providing expert opinion on measurement: {str(e)}")
                logger.debug(f"Exception details: {traceback.format_exc()}")

        if final_endpoint.endpoint_measurements:
            final_endpoints.append(final_endpoint)
                
    return {"final_endpoints": final_endpoints}

def build_parallel_pipeline():
    """Build the parallel extraction pipeline"""
    logger.info("Building parallel extraction pipeline")
    # 1) Instantiate graph
    graph = StateGraph(FinalState)

    # 2) Register nodes
    graph.add_node("extract_adcs", extract_adcs)
    graph.add_node("extract_all_models_in_text", extract_all_models_in_text)
    graph.add_node("extract_models", extract_models)
    graph.add_node("check_available_endpoint", check_available_endpoint)
    graph.add_node("create_models_per_endpoint", create_models_per_endpoint)
    graph.add_node("extract_endpoints", extract_endpoints)
    graph.add_node("extract_final_endpoints", extract_final_endpoints)

    # 3) Define edges
    graph.add_edge(START, "extract_adcs")
    graph.add_edge("extract_adcs", "extract_all_models_in_text")
    graph.add_conditional_edges("extract_all_models_in_text", continue_to_models, ["extract_models"])
    graph.add_conditional_edges("extract_models", continue_to_check_available_endpoint, ["check_available_endpoint"])
    graph.add_edge("check_available_endpoint", "create_models_per_endpoint")
    graph.add_conditional_edges("create_models_per_endpoint", continue_to_endpoints, ["extract_endpoints"])
    graph.add_edge("extract_endpoints", "extract_final_endpoints")
    graph.add_edge("extract_endpoints", END)
    
    return graph.compile()

async def process_file_parallel(md_file):
    """Process a markdown file using the parallel extraction pipeline"""
    start_time = time.time()
    
    # Extract filename from path
    filename = Path(md_file).name
    logger.info(f"Starting processing of file: {filename}")
    
    # Read the input file
    with open(md_file, 'r', encoding='utf-8') as f:
        text = f.read()
    
    # Initialize state with filename for filtering logic
    initial_state: FinalState = {
        "shared_context": SharedContext(raw_text=text, filename=filename),
        "adcs": [],
        "models": [],
        "available_endpoints": [],
        "endpoints": [],
        "final_endpoints": []
    }
    
    # Build and run pipeline
    compiled_graph = build_parallel_pipeline()
    # Asynchronously invoke the graph
    final_state = await compiled_graph.ainvoke(initial_state)
    
    end_time = time.time()
    processing_time = end_time - start_time
    logger.info(f"Completed processing of file: {filename} in {processing_time:.2f} seconds")
    
    return final_state['adcs'], final_state['models'], final_state['final_endpoints']

def save_results(adcs, models, endpoints, input_file, output_dir):
    """Save extraction results to JSON file."""
    try:
        logger.info(f"Saving results to {output_dir}")
        
        # Create output directory if it doesn't exist
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Create output filename based on input filename
        input_filename = Path(input_file).stem
        output_file = output_path / f"{input_filename}_results.json"
        
        output_adcs = [adc.model_dump() for adc in adcs]
        output_models = [model.model_dump() for model in models]
        output_endpoints = [endpoint.model_dump() for endpoint in endpoints]

        # Combine all results into a single list
        output_data = []
        # get_metrics("model", output_models)
        output_data.extend(output_adcs)
        output_data.extend(output_models)
        output_data.extend(output_endpoints)
        
        # Save to JSON file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Results saved successfully to {output_file}")
        
    except Exception as e:
        logger.error(f"Error saving results: {str(e)}")
        logger.debug(f"Error details: {traceback.format_exc()}")
        raise

async def process_directory_parallel(input_dir, output_dir):
    """Process all markdown files in a directory using the parallel extraction pipeline"""
    start_time = time.time()
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Get all markdown files
    md_files = list(input_path.glob("*.md"))
    total_files = len(md_files)
    
    if total_files == 0:
        logger.warning(f"No markdown files found in directory: {input_dir}")
        return
    
    logger.info(f"Found {total_files} markdown files to process in directory: {input_dir}")
    
    processed_count = 0
    failed_count = 0
    file_processing_times = []
    successful_extractions = {'adcs': [], 'models': [], 'endpoints': []}
    
    for md_file in tqdm(md_files, desc="Processing files"):
        file_start_time = time.time()
        filename = md_file.name  # Extract just the filename
        
        try:
            # Process file
            adcs, models, endpoints = await process_file_parallel(md_file)
            
            # Save results
            save_results(adcs, models, endpoints, str(md_file), output_path)
            
            file_end_time = time.time()
            file_processing_time = file_end_time - file_start_time
            file_processing_times.append(file_processing_time)
            
            # Track extraction counts
            successful_extractions['adcs'].append(len(adcs))
            successful_extractions['models'].append(len(models))
            successful_extractions['endpoints'].append(len(endpoints))
            
            logger.info(f"File: {filename} - Time: {file_processing_time:.2f}s - ADCs: {len(adcs)}, Models: {len(models)}, Endpoints: {len(endpoints)}")
            processed_count += 1
            
        except Exception as e:
            file_end_time = time.time()
            file_processing_time = file_end_time - file_start_time
            
            logger.error(f"File: {filename} - FAILED after {file_processing_time:.2f}s - Error: {str(e)}")
            logger.debug(f"Error details: {traceback.format_exc()}")
            failed_count += 1
            continue
    
    end_time = time.time()
    total_processing_time = end_time - start_time
    
    # Calculate and log comprehensive statistics
    logger.info("=" * 80)
    logger.info("DIRECTORY PROCESSING STATISTICS")
    logger.info("=" * 80)
    
    # Summary statistics
    logger.info(f"SUMMARY:")
    logger.info(f"  Total files: {total_files}")
    logger.info(f"  Successfully processed: {processed_count}")
    logger.info(f"  Failed: {failed_count}")
    logger.info(f"  Success rate: {(processed_count / total_files * 100):.1f}%")
    logger.info(f"  Total processing time: {total_processing_time:.2f} seconds ({total_processing_time/60:.1f} minutes)")
    logger.info(f"  Average time per file: {(total_processing_time/total_files):.2f} seconds")
    
    # Processing time statistics
    if file_processing_times:
        mean_time = statistics.mean(file_processing_times)
        median_time = statistics.median(file_processing_times)
        min_time = min(file_processing_times)
        max_time = max(file_processing_times)
        std_dev = statistics.stdev(file_processing_times) if len(file_processing_times) > 1 else 0
        
        # Calculate percentiles
        sorted_times = sorted(file_processing_times)
        p25 = sorted_times[int(0.25 * len(sorted_times))] if sorted_times else 0
        p75 = sorted_times[int(0.75 * len(sorted_times))] if sorted_times else 0
        p90 = sorted_times[int(0.90 * len(sorted_times))] if sorted_times else 0
        p95 = sorted_times[int(0.95 * len(sorted_times))] if sorted_times else 0
        
        logger.info(f"PROCESSING TIME STATISTICS:")
        logger.info(f"  Mean time: {mean_time:.2f} seconds")
        logger.info(f"  Median time: {median_time:.2f} seconds")
        logger.info(f"  Min time: {min_time:.2f} seconds")
        logger.info(f"  Max time: {max_time:.2f} seconds")
        logger.info(f"  Standard deviation: {std_dev:.2f} seconds")
        logger.info(f"  25th percentile: {p25:.2f} seconds")
        logger.info(f"  75th percentile: {p75:.2f} seconds")
        logger.info(f"  90th percentile: {p90:.2f} seconds")
        logger.info(f"  95th percentile: {p95:.2f} seconds")
    
    # Extraction statistics
    if processed_count > 0:
        total_adcs = sum(successful_extractions['adcs'])
        total_models = sum(successful_extractions['models'])
        total_endpoints = sum(successful_extractions['endpoints'])
        
        avg_adcs = statistics.mean(successful_extractions['adcs'])
        avg_models = statistics.mean(successful_extractions['models'])
        avg_endpoints = statistics.mean(successful_extractions['endpoints'])
        
        logger.info(f"EXTRACTION STATISTICS:")
        logger.info(f"  Total ADCs extracted: {total_adcs}")
        logger.info(f"  Total Models extracted: {total_models}")
        logger.info(f"  Total Endpoints extracted: {total_endpoints}")
        logger.info(f"  Average ADCs per file: {avg_adcs:.1f}")
        logger.info(f"  Average Models per file: {avg_models:.1f}")
        logger.info(f"  Average Endpoints per file: {avg_endpoints:.1f}")
        
        # Additional extraction insights
        if successful_extractions['adcs']:
            max_adcs = max(successful_extractions['adcs'])
            max_models = max(successful_extractions['models'])
            max_endpoints = max(successful_extractions['endpoints'])
            
            logger.info(f"  Max ADCs in single file: {max_adcs}")
            logger.info(f"  Max Models in single file: {max_models}")
            logger.info(f"  Max Endpoints in single file: {max_endpoints}")
    
    # Performance insights
    if file_processing_times and processed_count > 0:
        throughput = processed_count / total_processing_time
        logger.info(f"PERFORMANCE METRICS:")
        logger.info(f"  Processing throughput: {throughput:.2f} files/second")
        logger.info(f"  Processing throughput: {throughput * 60:.1f} files/minute")
        logger.info(f"  Processing throughput: {throughput * 3600:.0f} files/hour")
    
    logger.info("=" * 80)
    logger.info("END OF PROCESSING STATISTICS")
    logger.info("=" * 80)

async def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Extract information from scientific papers using parallel pipeline")
    
    # Input options - either single file or directory
    input_group = parser.add_mutually_exclusive_group(required=False)
    input_group.add_argument("--input-file", default='mds_200/W3182715110_review.md', type=str, help="Input markdown file to process")
    input_group.add_argument("--input-dir", type=str, help="Directory containing markdown files to process")
    
    parser.add_argument("--output-dir", type=str, default="output", help="Directory for output files")
    parser.add_argument("--logs-dir", type=str, default="logs", help="Directory for log files")
    parser.add_argument("--logging-level", type=str, default="DEBUG", 
                       choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                       help="Set logging level")
    args = parser.parse_args()
    
    # Setup logging (only once)
    global logger
    if logger is None:
        logger = setup_logging(logs_dir=args.logs_dir, logging_level=args.logging_level)
    
    logger.info("Starting parallel extraction pipeline")
    
    try:
        # Create output directory
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if args.input_file:
            # Process single file
            logger.info(f"Processing single file: {args.input_file}")
            adcs, models, endpoints = await process_file_parallel(args.input_file)
            
            # Save results to JSON
            save_results(adcs, models, endpoints, args.input_file, output_dir)
            
        elif args.input_dir:
            # Process directory
            logger.info(f"Processing directory: {args.input_dir}")
            await process_directory_parallel(args.input_dir, output_dir)
        
        logger.info("Extraction pipeline completed successfully")
        
    except Exception as e:
        logger.error(f"Fatal error in extraction pipeline: {str(e)}")
        logger.debug(f"Error details: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    # Initialize logger at module level (only once)
    if logger is None:
        logger = setup_logging()
    
    # Run the main function
    asyncio.run(main())
