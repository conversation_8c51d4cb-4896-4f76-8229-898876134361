#!/usr/bin/env python3
"""
Comparison script between hardcoded and dynamic MONDO standardizers
"""

import json
import time
from dynamic_mondo_standardizer import DynamicMondoStandardizer
from mondo_standardizer import Mon<PERSON><PERSON><PERSON>dardi<PERSON>


def compare_standardizers():
    """Compare the two standardizer approaches."""
    print("MONDO Standardizer Comparison")
    print("=" * 60)
    
    # Test cases covering various cancer terminology
    test_cases = [
        # Basic cancer types
        "Breast cancer",
        "Colorectal cancer", 
        "Lung cancer",
        "Pancreatic cancer",
        
        # Abbreviations
        "NSCLC",
        "CRC", 
        "HCC",
        "PDAC",
        
        # Variations and formatting
        "Non-small cell lung cancer (NSCLC)",
        "Non–small cell lung cancer (NSCLC)",  # Different dash
        "Breast cancer (mouse model)",
        "Human colorectal adenocarcinoma cell line",
        
        # Subtypes
        "Adenocarcinoma",
        "Carcinoma",
        "Mammary carcinoma",
        "Squamous cell carcinoma",
        
        # Edge cases
        "Not applicable",
        "Fibroblast (non-cancer, human-derived)",
        "Protein assay",
        
        # Complex terms
        "Triple negative breast cancer",
        "Glioblastoma multiforme",
        "Diffuse large B-cell lymphoma",
        "Acute myeloid leukemia"
    ]
    
    # Initialize standardizers
    print("Initializing standardizers...")
    hardcoded_standardizer = MondoStandardizer()
    dynamic_standardizer = DynamicMondoStandardizer(confidence_threshold=0.7)
    
    print(f"\nTesting {len(test_cases)} cancer terms...")
    print("-" * 60)
    
    results = []
    
    for i, term in enumerate(test_cases):
        print(f"\n{i+1:2d}. Testing: '{term}'")
        
        # Test hardcoded standardizer
        hardcoded_start = time.time()
        hardcoded_mondo_id, hardcoded_result = hardcoded_standardizer.standardize_cancer_type(term)
        hardcoded_time = time.time() - hardcoded_start
        
        # Test dynamic standardizer
        dynamic_start = time.time()
        dynamic_result = dynamic_standardizer.standardize_cancer_term(term)
        dynamic_time = time.time() - dynamic_start
        
        # Compare results
        print(f"    Hardcoded: '{hardcoded_result}' ({hardcoded_mondo_id}) [{hardcoded_time:.3f}s]")
        
        if dynamic_result:
            print(f"    Dynamic:   '{dynamic_result.term}' ({dynamic_result.mondo_id}) "
                  f"[{dynamic_time:.3f}s, conf: {dynamic_result.confidence:.2f}, {dynamic_result.match_type}]")
        else:
            print(f"    Dynamic:   No match found [{dynamic_time:.3f}s]")
        
        # Store results for analysis
        results.append({
            'term': term,
            'hardcoded_result': hardcoded_result,
            'hardcoded_mondo_id': hardcoded_mondo_id,
            'hardcoded_time': hardcoded_time,
            'dynamic_result': dynamic_result.term if dynamic_result else None,
            'dynamic_mondo_id': dynamic_result.mondo_id if dynamic_result else None,
            'dynamic_confidence': dynamic_result.confidence if dynamic_result else 0.0,
            'dynamic_match_type': dynamic_result.match_type if dynamic_result else None,
            'dynamic_time': dynamic_time
        })
        
        # Rate limiting for API calls
        if dynamic_result:
            time.sleep(0.2)
    
    # Analysis
    print("\n" + "=" * 60)
    print("ANALYSIS")
    print("=" * 60)
    
    # Coverage comparison
    hardcoded_matches = sum(1 for r in results if not r['hardcoded_mondo_id'].startswith('UNMAPPED'))
    dynamic_matches = sum(1 for r in results if r['dynamic_result'] is not None)
    
    print(f"Coverage:")
    print(f"  Hardcoded standardizer: {hardcoded_matches}/{len(test_cases)} ({hardcoded_matches/len(test_cases)*100:.1f}%)")
    print(f"  Dynamic standardizer:   {dynamic_matches}/{len(test_cases)} ({dynamic_matches/len(test_cases)*100:.1f}%)")
    
    # Performance comparison
    avg_hardcoded_time = sum(r['hardcoded_time'] for r in results) / len(results)
    avg_dynamic_time = sum(r['dynamic_time'] for r in results) / len(results)
    
    print(f"\nPerformance:")
    print(f"  Hardcoded standardizer: {avg_hardcoded_time:.3f}s average")
    print(f"  Dynamic standardizer:   {avg_dynamic_time:.3f}s average")
    
    # Agreement analysis
    agreements = 0
    for r in results:
        if r['hardcoded_result'] and r['dynamic_result']:
            # Normalize for comparison
            h_norm = r['hardcoded_result'].lower().strip()
            d_norm = r['dynamic_result'].lower().strip()
            if h_norm == d_norm:
                agreements += 1
    
    comparable_cases = sum(1 for r in results if r['hardcoded_result'] and r['dynamic_result'])
    if comparable_cases > 0:
        agreement_rate = agreements / comparable_cases * 100
        print(f"\nAgreement:")
        print(f"  {agreements}/{comparable_cases} cases agree ({agreement_rate:.1f}%)")
    
    # Unique capabilities
    print(f"\nUnique capabilities:")
    hardcoded_only = [r for r in results if r['hardcoded_result'] and not r['dynamic_result']]
    dynamic_only = [r for r in results if not r['hardcoded_result'] and r['dynamic_result']]
    
    if hardcoded_only:
        print(f"  Hardcoded-only matches: {len(hardcoded_only)}")
        for r in hardcoded_only[:3]:  # Show first 3
            print(f"    '{r['term']}' → '{r['hardcoded_result']}'")
    
    if dynamic_only:
        print(f"  Dynamic-only matches: {len(dynamic_only)}")
        for r in dynamic_only[:3]:  # Show first 3
            print(f"    '{r['term']}' → '{r['dynamic_result']}' (conf: {r['dynamic_confidence']:.2f})")
    
    # Confidence distribution for dynamic standardizer
    dynamic_confidences = [r['dynamic_confidence'] for r in results if r['dynamic_confidence'] > 0]
    if dynamic_confidences:
        avg_confidence = sum(dynamic_confidences) / len(dynamic_confidences)
        print(f"\nDynamic standardizer confidence:")
        print(f"  Average confidence: {avg_confidence:.3f}")
        print(f"  High confidence (≥0.9): {sum(1 for c in dynamic_confidences if c >= 0.9)}/{len(dynamic_confidences)}")
    
    return results


def demonstrate_scalability():
    """Demonstrate scalability advantages of dynamic standardizer."""
    print("\n" + "=" * 60)
    print("SCALABILITY DEMONSTRATION")
    print("=" * 60)
    
    # Simulate diverse cancer terminology from literature
    diverse_terms = [
        # Real-world variations found in literature
        "invasive ductal carcinoma",
        "triple-negative breast cancer", 
        "estrogen receptor-positive breast cancer",
        "her2-positive breast cancer",
        "small cell lung cancer",
        "squamous cell lung carcinoma",
        "lung adenocarcinoma",
        "bronchioloalveolar carcinoma",
        "pancreatic ductal adenocarcinoma",
        "pancreatic neuroendocrine tumor",
        "hepatocellular carcinoma",
        "cholangiocarcinoma",
        "renal cell carcinoma",
        "clear cell renal carcinoma",
        "urothelial carcinoma",
        "prostate adenocarcinoma",
        "castration-resistant prostate cancer",
        "ovarian serous carcinoma",
        "endometrial adenocarcinoma",
        "cervical squamous cell carcinoma",
        "gastric adenocarcinoma",
        "esophageal adenocarcinoma",
        "head and neck squamous cell carcinoma",
        "oral squamous cell carcinoma",
        "cutaneous melanoma",
        "uveal melanoma",
        "glioblastoma multiforme",
        "oligodendroglioma",
        "meningioma",
        "medulloepithelioma",
        "acute lymphoblastic leukemia",
        "chronic lymphocytic leukemia",
        "multiple myeloma",
        "mantle cell lymphoma",
        "follicular lymphoma",
        "burkitt lymphoma",
        "osteosarcoma",
        "ewing sarcoma",
        "rhabdomyosarcoma",
        "liposarcoma"
    ]
    
    print(f"Testing dynamic standardizer with {len(diverse_terms)} diverse cancer terms...")
    
    # Test with dynamic standardizer only (hardcoded would fail on most)
    dynamic_standardizer = DynamicMondoStandardizer(confidence_threshold=0.6)
    
    successful_matches = 0
    high_confidence_matches = 0
    total_time = 0
    
    for i, term in enumerate(diverse_terms):
        start_time = time.time()
        result = dynamic_standardizer.standardize_cancer_term(term)
        elapsed_time = time.time() - start_time
        total_time += elapsed_time
        
        if result:
            successful_matches += 1
            if result.confidence >= 0.8:
                high_confidence_matches += 1
            
            if i < 5:  # Show first 5 results
                print(f"  '{term}' → '{result.term}' ({result.mondo_id}) "
                      f"[conf: {result.confidence:.2f}, {result.match_type}]")
        
        # Rate limiting
        time.sleep(0.1)
    
    print(f"  ... (showing first 5 results)")
    print(f"\nResults:")
    print(f"  Successful matches: {successful_matches}/{len(diverse_terms)} ({successful_matches/len(diverse_terms)*100:.1f}%)")
    print(f"  High confidence (≥0.8): {high_confidence_matches}/{len(diverse_terms)} ({high_confidence_matches/len(diverse_terms)*100:.1f}%)")
    print(f"  Average processing time: {total_time/len(diverse_terms):.3f}s per term")
    print(f"  Total processing time: {total_time:.1f}s")
    
    # Get final report
    report = dynamic_standardizer.get_standardization_report()
    print(f"\nCache efficiency:")
    print(f"  Total API requests: {report['api_requests_made']}")
    print(f"  Cached terms: {report['total_cached_terms']}")
    print(f"  Cache hit ratio: {report['successful_matches']/max(report['total_cached_terms'], 1):.2f}")


def main():
    """Run the comparison and demonstration."""
    try:
        # Compare standardizers
        results = compare_standardizers()
        
        # Demonstrate scalability
        demonstrate_scalability()
        
        print("\n" + "=" * 60)
        print("CONCLUSION")
        print("=" * 60)
        print("The dynamic MONDO standardizer provides:")
        print("✅ Comprehensive coverage of cancer terminology")
        print("✅ No hardcoded mappings to maintain")
        print("✅ Confidence scoring for quality control")
        print("✅ Scalability for diverse biomedical literature")
        print("✅ Real-time access to latest MONDO ontology")
        print("✅ Intelligent caching for performance")
        
        print("\nRecommendation: Use dynamic standardizer for production")
        print("systems processing diverse cancer terminology.")
        
    except Exception as e:
        print(f"Error during comparison: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
