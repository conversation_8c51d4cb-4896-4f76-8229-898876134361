#!/usr/bin/env python3
"""
Basic MONDO Standardizer for ADC Results JSON

This script standardizes cancer type and subtype fields in ADC results JSON files
using the MONDO (Monarch Disease Ontology) ontology.

Usage:
    python mondo_standardizer.py input_results.json output_results.json
"""

import json
import sys
import re
import copy
from typing import Dict, Any, Optional, Tuple


class MondoStandardizer:
    """Basic MONDO standardizer for cancer types and subtypes."""
    
    def __init__(self):
        """Initialize the standardizer with cancer type mappings."""
        # Basic MONDO cancer type mappings
        # Format: {input_term: (mondo_id, standardized_name)}
        self.cancer_type_mappings = {
            # Breast cancer
            "breast cancer": ("MONDO:0007254", "breast cancer"),
            "breast cancer (mouse model)": ("MONDO:0007254", "breast cancer"),
            
            # Colorectal cancer
            "colorectal cancer": ("MONDO:0005575", "colorectal cancer"),
            
            # Lung cancer
            "non-small cell lung cancer (nsclc)": ("MONDO:0005233", "non-small cell lung cancer"),
            "non–small cell lung cancer (nsclc)": ("MONDO:0005233", "non-small cell lung cancer"),
            "lung cancer": ("MONDO:0008903", "lung cancer"),
            
            # Pancreatic cancer
            "pancreatic cancer": ("MONDO:0005192", "pancreatic cancer"),
            
            # General cancer
            "cancer": ("MONDO:0004992", "cancer"),
            
            # Non-cancer/Not applicable
            "not applicable": ("N/A", "not applicable"),
            "not applicable (protein-based binding assay)": ("N/A", "not applicable"),
            "not applicable (safety/pharmacokinetics)": ("N/A", "not applicable"),
            "fibroblast (non-cancer, human-derived)": ("N/A", "not applicable"),
        }
        
        # Basic MONDO cancer subtype mappings
        # Format: {input_term: (mondo_id, standardized_name)}
        self.cancer_subtype_mappings = {
            # Adenocarcinoma
            "adenocarcinoma": ("MONDO:0004970", "adenocarcinoma"),
            
            # Carcinoma
            "carcinoma": ("MONDO:0004993", "carcinoma"),
            "mammary carcinoma": ("MONDO:0002067", "mammary carcinoma"),
            "mammary carcinoma (rechallenge/immune memory)": ("MONDO:0002067", "mammary carcinoma"),
            
            # Null/None values
            None: (None, None),
            "null": (None, None),
        }
    
    def normalize_text(self, text: str) -> str:
        """Normalize text for matching."""
        if not text or text == "null":
            return ""
        return text.lower().strip()
    
    def standardize_cancer_type(self, cancer_type: str) -> Tuple[str, str]:
        """
        Standardize cancer type using MONDO ontology.
        
        Args:
            cancer_type: Original cancer type string
            
        Returns:
            Tuple of (mondo_id, standardized_name)
        """
        if not cancer_type or cancer_type == "null":
            return "N/A", "not applicable"
        
        normalized = self.normalize_text(cancer_type)
        
        # Direct mapping lookup
        if normalized in self.cancer_type_mappings:
            return self.cancer_type_mappings[normalized]
        
        # Fuzzy matching for common patterns
        if "breast" in normalized:
            return "MONDO:0007254", "breast cancer"
        elif "colorectal" in normalized:
            return "MONDO:0005575", "colorectal cancer"
        elif "lung" in normalized and "non-small" in normalized:
            return "MONDO:0005233", "non-small cell lung cancer"
        elif "lung" in normalized:
            return "MONDO:0008903", "lung cancer"
        elif "pancreatic" in normalized:
            return "MONDO:0005192", "pancreatic cancer"
        elif "cancer" in normalized:
            return "MONDO:0004992", "cancer"
        
        # If no match found, return original with note
        return f"UNMAPPED:{cancer_type}", cancer_type
    
    def standardize_cancer_subtype(self, cancer_subtype: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Standardize cancer subtype using MONDO ontology.
        
        Args:
            cancer_subtype: Original cancer subtype string
            
        Returns:
            Tuple of (mondo_id, standardized_name)
        """
        if not cancer_subtype or cancer_subtype == "null":
            return None, None
        
        normalized = self.normalize_text(cancer_subtype)
        
        # Direct mapping lookup
        if normalized in self.cancer_subtype_mappings:
            return self.cancer_subtype_mappings[normalized]
        
        # Fuzzy matching for common patterns
        if "adenocarcinoma" in normalized:
            return "MONDO:0004970", "adenocarcinoma"
        elif "carcinoma" in normalized:
            return "MONDO:0004993", "carcinoma"
        
        # If no match found, return original with note
        return f"UNMAPPED:{cancer_subtype}", cancer_subtype
    
    def standardize_entry(self, entry: Dict[str, Any]) -> Dict[str, Any]:
        """
        Standardize a single entry in the results JSON.

        Args:
            entry: Dictionary representing one entry

        Returns:
            Standardized entry dictionary
        """
        # Create a deep copy to avoid modifying the original
        standardized_entry = copy.deepcopy(entry)

        # Check if this entry has a model section with cancer fields
        if "model" in entry and isinstance(entry["model"], dict):
            model = entry["model"]

            # Standardize cancer_type
            if "cancer_type" in model:
                original_cancer_type = model["cancer_type"]
                mondo_id, standardized_name = self.standardize_cancer_type(original_cancer_type)
                standardized_entry["model"]["cancer_type"] = standardized_name

                # Add citation and reasoning if standardization occurred and it's a valid MONDO term
                if mondo_id.startswith("MONDO:"):
                    # Add to citations if not already present
                    citations = standardized_entry["model"].get("citations", [])
                    mondo_citation = f"Cancer type standardized to MONDO ontology: {mondo_id} ({standardized_name})"
                    if mondo_citation not in citations:
                        citations.append(mondo_citation)
                        standardized_entry["model"]["citations"] = citations

            # Standardize cancer_subtype
            if "cancer_subtype" in model:
                original_cancer_subtype = model["cancer_subtype"]
                mondo_id, standardized_name = self.standardize_cancer_subtype(original_cancer_subtype)
                standardized_entry["model"]["cancer_subtype"] = standardized_name

                # Add citation and reasoning if standardization occurred and it's a valid MONDO term
                if mondo_id and mondo_id.startswith("MONDO:"):
                    # Add to citations if not already present
                    citations = standardized_entry["model"].get("citations", [])
                    mondo_citation = f"Cancer subtype standardized to MONDO ontology: {mondo_id} ({standardized_name})"
                    if mondo_citation not in citations:
                        citations.append(mondo_citation)
                        standardized_entry["model"]["citations"] = citations

        return standardized_entry
    
    def standardize_results(self, results_data: list) -> list:
        """
        Standardize all entries in the results JSON.
        
        Args:
            results_data: List of result entries
            
        Returns:
            List of standardized entries
        """
        standardized_results = []
        
        for entry in results_data:
            standardized_entry = self.standardize_entry(entry)
            standardized_results.append(standardized_entry)
        
        return standardized_results
    
    def process_file(self, input_file: str, output_file: str) -> None:
        """
        Process a JSON file and save standardized results.
        
        Args:
            input_file: Path to input JSON file
            output_file: Path to output JSON file
        """
        try:
            # Read input file
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Standardize the data
            standardized_data = self.standardize_results(data)
            
            # Write output file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(standardized_data, f, indent=2, ensure_ascii=False)
            
            print(f"Successfully standardized {len(standardized_data)} entries")
            print(f"Input: {input_file}")
            print(f"Output: {output_file}")
            
        except FileNotFoundError:
            print(f"Error: Input file '{input_file}' not found.")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"Error: Invalid JSON in input file: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"Error processing file: {e}")
            sys.exit(1)


def main():
    """Main function to run the standardizer."""
    if len(sys.argv) != 3:
        print("Usage: python mondo_standardizer.py <input_file.json> <output_file.json>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    standardizer = MondoStandardizer()
    standardizer.process_file(input_file, output_file)


if __name__ == "__main__":
    main()
