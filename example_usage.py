#!/usr/bin/env python3
"""
Example usage of the MONDO standardizer
"""

from mondo_standardizer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import json

def main():
    """Demonstrate basic usage of the MONDO standardizer."""
    
    print("MONDO Standardizer Example")
    print("=" * 40)
    
    # Create a standardizer instance
    standardizer = MondoStandardizer()
    
    # Example 1: Standardize individual cancer types
    print("\n1. Individual cancer type standardization:")
    cancer_types = [
        "Breast cancer",
        "Non-small cell lung cancer (NSCLC)",
        "Colorectal cancer",
        "Pancreatic cancer",
        "Not applicable"
    ]
    
    for cancer_type in cancer_types:
        mondo_id, standardized_name = standardizer.standardize_cancer_type(cancer_type)
        print(f"  '{cancer_type}' → '{standardized_name}' ({mondo_id})")
    
    # Example 2: Standardize cancer subtypes
    print("\n2. Individual cancer subtype standardization:")
    cancer_subtypes = [
        "Adenocarcinoma",
        "Carcinoma", 
        "Mammary carcinoma",
        None
    ]
    
    for subtype in cancer_subtypes:
        mondo_id, standardized_name = standardizer.standardize_cancer_subtype(subtype)
        print(f"  '{subtype}' → '{standardized_name}' ({mondo_id})")
    
    # Example 3: Process a sample JSON entry
    print("\n3. Processing a complete JSON entry:")
    sample_entry = {
        "adc": {
            "adc_name": "Example-ADC",
            "adc_company": "Example Company"
        },
        "model": {
            "citations": ["Original research citation"],
            "model_name": "Example cancer cell line",
            "cancer_type": "Breast cancer",
            "cancer_subtype": "Adenocarcinoma",
            "investigative": True,
            "reasoning_for_inclusion": "Example reasoning"
        }
    }
    
    print("Original entry:")
    print(json.dumps(sample_entry, indent=2))
    
    standardized_entry = standardizer.standardize_entry(sample_entry)
    
    print("\nStandardized entry:")
    print(json.dumps(standardized_entry, indent=2))
    
    # Example 4: Show supported mappings
    print("\n4. Currently supported cancer type mappings:")
    print("   Cancer Type → MONDO ID (Standardized Name)")
    print("   " + "-" * 50)
    for original, (mondo_id, standardized) in standardizer.cancer_type_mappings.items():
        if mondo_id.startswith("MONDO:"):
            print(f"   '{original}' → {mondo_id} ({standardized})")
    
    print("\n5. Currently supported cancer subtype mappings:")
    print("   Cancer Subtype → MONDO ID (Standardized Name)")
    print("   " + "-" * 50)
    for original, (mondo_id, standardized) in standardizer.cancer_subtype_mappings.items():
        if mondo_id and mondo_id.startswith("MONDO:"):
            print(f"   '{original}' → {mondo_id} ({standardized})")

if __name__ == "__main__":
    main()
