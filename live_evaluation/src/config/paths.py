import os
from pathlib import Path


def get_base_data_path():
    """
    Get base data path based on environment detection.
    
    Priority order:
    1. ADC_DATA_PATH environment variable (absolute override)
    2. DOMINO_DATASETS_DIR environment variable + /live_evaluation/data
    3. DOMINO_PROJECT_NAME detection (legacy) → /domino/datasets/local/adc_data_center/live_evaluation/data/
    4. Development: ./data/ relative to project root
    
    Returns:
        Path: Base data directory path (absolute)
    """
    # Priority 1: Direct override via ADC_DATA_PATH
    if os.environ.get('ADC_DATA_PATH'):
        return Path(os.environ['ADC_DATA_PATH']).absolute()
    
    # Priority 2: DOMINO_DATASETS_DIR (more flexible than hardcoded path)
    if os.environ.get('DOMINO_DATASETS_DIR'):
        base_dir = Path(os.environ['DOMINO_DATASETS_DIR'])
        return (base_dir / 'live_evaluation' / 'data').absolute()
    
    # Priority 3: Legacy Domino detection
    if os.environ.get('DOMINO_PROJECT_NAME'):
        return Path('/domino/datasets/local/adc_data_center/live_evaluation/data').absolute()
    
    # Priority 4: Development environment - relative to project root
    project_root = Path(__file__).parent.parent.parent
    return (project_root / 'data').absolute()


def get_annotations_path():
    """Get the annotations data directory path."""
    return get_base_data_path() / 'annotations'


def get_evaluations_path():
    """Get the evaluations data directory path."""
    return get_base_data_path() / 'evaluations'


def ensure_path_exists(path):
    """
    Ensure that the given path exists, creating directories if needed.
    
    Args:
        path (Path): Path to ensure exists
        
    Returns:
        Path: The ensured path
    """
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path