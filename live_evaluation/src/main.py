#!/usr/bin/env python3
"""
ADC Live Evaluator - Main Orchestration Script

Orchestrates the complete ADC evaluation pipeline including test data preparation,
initialization, and GPT-based evaluation with parallel processing.
"""

import argparse
import logging
import sys
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Optional

# Add the src directory to path for imports
sys.path.append(str(Path(__file__).parent))
from config.paths import get_evaluations_path, ensure_path_exists
from evaluation.config import (
    load_latest_annotations,
    select_fallback_examples,
    check_resume_scenario,
    generate_evaluation_config,
    save_evaluation_config,
    load_evaluation_config
)
from evaluation.evaluator import EvaluationEngine
from postprocessing.xlsx_converter import convert_results_to_xlsx
from postprocessing.precision_calculator import calculate_precision_metrics


def setup_logging(log_file_path: Path) -> logging.Logger:
    """
    Setup logging for the main orchestration script.
    
    Args:
        log_file_path: Path to log file
        
    Returns:
        Configured logger
    """
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    file_handler = logging.FileHandler(log_file_path)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


def generate_timestamp() -> str:
    """Generate timestamp in YYYYMMDD_HHMMSS format."""
    return datetime.now().strftime("%Y%m%d_%H%M%S")


def run_test_data_prep(test_data_path: Path, timestamp: str, logger: logging.Logger) -> bool:
    """
    Run test data preparation step.
    
    Args:
        test_data_path: Path to test data XLSX file
        timestamp: Timestamp for this evaluation run
        logger: Logger instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("=== STAGE 1: TEST DATA PREPARATION ===")
    
    # Build command to run test_data_prep.py
    script_path = Path(__file__).parent / "preprocessing" / "test_data_prep.py"
    
    cmd = [
        sys.executable,
        str(script_path),
        str(test_data_path),
        "--output-timestamp", timestamp
    ]
    
    logger.info(f"Running: {' '.join(cmd)}")
    
    try:
        # Run test data preparation
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path.cwd())
        
        # Log output
        if result.stdout:
            for line in result.stdout.strip().split('\n'):
                logger.info(f"[test_data_prep] {line}")
        
        if result.stderr:
            for line in result.stderr.strip().split('\n'):
                logger.warning(f"[test_data_prep] {line}")
        
        if result.returncode == 0:
            logger.info("Test data preparation completed successfully")
            return True
        else:
            logger.error(f"Test data preparation failed with return code: {result.returncode}")
            return False
            
    except Exception as e:
        logger.error(f"Error running test data preparation: {e}")
        return False


def run_initialization(
    evaluation_dir: Path,
    output_dir: Optional[Path],
    parallel_workers: int,
    timestamp: str,
    logger: logging.Logger
) -> bool:
    """
    Run initialization phase - load annotations, select fallback examples,
    handle resume logic, and generate/load config.json.
    
    Args:
        evaluation_dir: Current evaluation directory
        output_dir: Optional output directory for resume
        parallel_workers: Number of parallel workers
        timestamp: Current evaluation timestamp
        logger: Logger instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("=== STAGE 2 PHASE 2: INITIALIZATION ===")
    
    try:
        # Check resume scenario first
        is_resume, partial_results_path = check_resume_scenario(output_dir)
        
        if is_resume:
            # Load existing configuration
            config_path = output_dir / "output" / "config.json"
            if not config_path.exists():
                logger.error(f"Resume requested but config.json not found: {config_path}")
                return False
                
            config = load_evaluation_config(config_path)
            logger.info("Loaded existing configuration for resume")
            
        else:
            # New evaluation run - generate configuration
            logger.info("Starting new evaluation run")
            
            # Load latest annotations
            logger.info("Loading latest training annotations...")
            annotations_data, annotations_timestamp = load_latest_annotations()
            
            # Select fallback examples
            logger.info("Selecting fallback examples...")
            fallback_examples = select_fallback_examples(annotations_data)
            
            # Generate configuration
            config = generate_evaluation_config(
                timestamp=timestamp,
                test_data_path=Path("placeholder"),  # Will be updated with actual path
                annotations_timestamp=annotations_timestamp,
                fallback_examples=fallback_examples,
                parallel_workers=parallel_workers,
                is_resume=False,
                last_processed_index=0
            )
            
            # Save configuration
            output_subdir = evaluation_dir / "output"
            config_path = output_subdir / "config.json"
            save_evaluation_config(config, config_path)
            
        logger.info("Initialization completed successfully")
        logger.info(f"Configuration: {config['timestamp']}, Workers: {config['parallel_workers']}")
        logger.info(f"Fallback examples: {len(config['fallback_examples']['tp_examples'])} TP, "
                   f"{len(config['fallback_examples']['fp_examples'])} FP")
        
        return True
        
    except Exception as e:
        logger.error(f"Initialization failed: {e}")
        return False


def run_evaluation(
    evaluation_dir: Path,
    output_dir: Optional[Path],
    parallel_workers: int,
    logger: logging.Logger
) -> bool:
    """
    Run GPT evaluation phase with parallel processing and checkpointing.
    
    Args:
        evaluation_dir: Current evaluation directory
        output_dir: Optional output directory for resume
        parallel_workers: Number of parallel workers
        logger: Logger instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("=== STAGE 2 PHASE 3: GPT EVALUATION ===")
    
    try:
        # Check if this is a resume scenario
        is_resume, partial_results_path = check_resume_scenario(output_dir)
        
        # Initialize evaluation engine
        engine = EvaluationEngine(evaluation_dir, parallel_workers)
        
        # Run evaluation (engine handles resume logic internally)
        summary = engine.run_evaluation(resume=is_resume)
        
        # Log summary
        logger.info("Evaluation completed successfully!")
        logger.info(f"Total processed: {summary['processed_extractions']}/{summary['total_extractions']}")
        logger.info(f"Successful: {summary['successful_evaluations']}, Failed: {summary['failed_evaluations']}")
        logger.info(f"TP: {summary['tp_predictions']}, FP: {summary['fp_predictions']}")
        logger.info(f"Precision: {summary['precision']:.3f}")
        
        return True
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        return False


def run_postprocessing(evaluation_dir: Path, logger: logging.Logger) -> bool:
    """
    Run Stage 3: Postprocessing to generate XLSX outputs.
    
    Args:
        evaluation_dir: Path to evaluation directory
        logger: Logger instance
        
    Returns:
        bool: True if postprocessing successful, False otherwise
    """
    try:
        output_dir = evaluation_dir / "output"
        
        logger.info("=== STAGE 3: POSTPROCESSING ===")
        
        # Convert full_results.json to results.xlsx
        logger.info("Converting results to XLSX format...")
        xlsx_path = convert_results_to_xlsx(output_dir)
        logger.info(f"Results XLSX created: {xlsx_path}")
        
        # Calculate precision metrics
        logger.info("Calculating precision metrics...")
        endpoint_path, paper_path = calculate_precision_metrics(output_dir)
        logger.info(f"Endpoint precision created: {endpoint_path}")
        logger.info(f"Paper precision created: {paper_path}")
        
        logger.info("=== STAGE 3 COMPLETE ===")
        logger.info("Postprocessing completed successfully - all output files generated")
        
        return True
        
    except Exception as e:
        logger.error(f"Postprocessing failed: {e}")
        return False


def validate_environment():
    """Validate environment configuration and paths."""
    import os
    from config.paths import get_base_data_path, get_annotations_path, get_evaluations_path
    
    # Check data paths
    base_path = get_base_data_path()
    annotations_path = get_annotations_path()
    evaluations_path = get_evaluations_path()
    
    print(f"Environment Configuration:")
    print(f"  Base data path: {base_path}")
    print(f"  Annotations path: {annotations_path}")
    print(f"  Evaluations path: {evaluations_path}")
    
    # Check if we're in production (Domino)
    if os.environ.get('DOMINO_PROJECT_NAME') or os.environ.get('DOMINO_DATASETS_DIR'):
        print(f"  Environment: PRODUCTION (Domino)")
    else:
        print(f"  Environment: DEVELOPMENT")
    
    # Check Azure credentials
    has_credentials = bool(
        os.environ.get('AZURE_OPENAI_API_KEY') and 
        os.environ.get('AZURE_OPENAI_ENDPOINT')
    )
    
    if not has_credentials:
        print("WARNING: Azure OpenAI credentials not found in environment")
        print("  Please set AZURE_OPENAI_API_KEY and AZURE_OPENAI_ENDPOINT")
        print("  Or ensure .env file is accessible")
    else:
        print("  Azure credentials: CONFIGURED")
    
    return has_credentials

def main():
    """Main orchestration function."""
    parser = argparse.ArgumentParser(description="ADC Live Evaluator - Complete Evaluation Pipeline")
    parser.add_argument("--test-data", required=True, help="Path to test data XLSX file")
    parser.add_argument("--parallel-workers", type=int, default=3, 
                       help="Number of parallel workers (1-5, default: 3)")
    parser.add_argument("--output-dir", help="Resume from existing evaluation folder")
    parser.add_argument("--log-file", help="Path to main log file (defaults to output folder)")
    
    args = parser.parse_args()
    
    # Validate environment first
    has_credentials = validate_environment()
    if not has_credentials:
        print("\nERROR: Cannot proceed without Azure OpenAI credentials")
        return 1
    
    print()  # Blank line after environment info
    
    # Check if this is a resume scenario first
    output_dir_arg = Path(args.output_dir) if args.output_dir else None
    is_resume_check, _ = check_resume_scenario(output_dir_arg)
    
    if is_resume_check:
        # Resume scenario - use existing evaluation directory
        evaluation_dir = output_dir_arg
        output_dir = evaluation_dir / "output"
        timestamp = evaluation_dir.name  # Use existing timestamp
        
        # Test data path not needed for resume, but validate anyway
        test_data_path = Path(args.test_data)
        if not test_data_path.exists():
            print(f"ERROR: Test data file not found: {test_data_path}")
            return 1
            
    else:
        # New evaluation run
        # Validate test data file
        test_data_path = Path(args.test_data)
        if not test_data_path.exists():
            print(f"ERROR: Test data file not found: {test_data_path}")
            return 1
        
        # Generate timestamp for this evaluation run
        timestamp = generate_timestamp()
        
        # Create evaluation folder structure
        evaluations_base = get_evaluations_path()
        evaluation_dir = ensure_path_exists(evaluations_base / timestamp)
        output_dir = ensure_path_exists(evaluation_dir / "output")
    
    # Setup main logging
    log_file_path = Path(args.log_file) if args.log_file else output_dir / "evaluation_pipeline.log"
    logger = setup_logging(log_file_path)
    
    logger.info("=== ADC LIVE EVALUATOR STARTED ===")
    logger.info(f"Resume mode: {is_resume_check}")
    logger.info(f"Test data: {test_data_path}")
    logger.info(f"Evaluation timestamp: {timestamp}")
    logger.info(f"Evaluation directory: {evaluation_dir}")
    logger.info(f"Main log file: {log_file_path}")
    
    if not is_resume_check:
        # Phase 1: Test Data Processing (only for new runs)
        logger.info("=== STAGE 1: TEST DATA PREPARATION ===")
        success = run_test_data_prep(test_data_path, timestamp, logger)
        
        if not success:
            logger.error("Test data preparation failed")
            return 1
        
        logger.info("=== PHASE 1 COMPLETE ===")
        logger.info("Test data has been processed successfully")
    else:
        logger.info("=== RESUMING EVALUATION ===")
        logger.info("Skipping test data preparation - using existing processed data")
    
    # Phase 2: Initialization
    success = run_initialization(evaluation_dir, output_dir_arg, args.parallel_workers, timestamp, logger)
    
    if not success:
        logger.error("Initialization failed")
        return 1
    
    # Phase 3: GPT Evaluation
    success = run_evaluation(evaluation_dir, output_dir_arg, args.parallel_workers, logger)
    
    if not success:
        logger.error("Evaluation failed")
        return 1
    
    # Stage 2 Complete
    logger.info("=== STAGE 2 COMPLETE ===")
    logger.info("Evaluation completed successfully - all extractions processed with GPT evaluation")
    logger.info(f"Evaluation folder: {evaluation_dir}")
    logger.info("Results saved to full_results.json")
    
    # Stage 3: Postprocessing
    success = run_postprocessing(evaluation_dir, logger)
    
    if not success:
        logger.error("Postprocessing failed")
        return 1
    
    # All stages complete
    logger.info("=== ALL STAGES COMPLETE ===")
    logger.info("ADC Live Evaluator completed successfully - evaluation and postprocessing finished")
    
    print(f"\n=== ADC LIVE EVALUATOR - ALL STAGES COMPLETE ===")
    print(f"Evaluation and postprocessing completed successfully")
    print(f"Evaluation folder: {evaluation_dir}")
    print(f"Main log file: {log_file_path}")
    print(f"Results available in: {evaluation_dir / 'output' / 'full_results.json'}")
    print(f"XLSX outputs:")
    print(f"  - {evaluation_dir / 'output' / 'results.xlsx'}")
    print(f"  - {evaluation_dir / 'output' / 'endpoint_precision.xlsx'}")
    print(f"  - {evaluation_dir / 'output' / 'paper_precision.xlsx'}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())