import pandas as pd
from pathlib import Path
import logging
from typing import Optional


logger = logging.getLogger(__name__)


class ExtractionValidator:
    """Validates extraction/test data XLSX files for ADC evaluation."""
    
    # Required columns that must have non-null values (after processing)
    REQUIRED_NON_NULL_COLUMNS = [
        'paper_id',  # Mapped from 'id'
        'extraction_id',  # Generated during processing
        'adc_name',
        'model_name',
        'model_type',
        'experiment_type',
        'endpoint_name',
        'measured_value',
        'support_span'  # Mapped from 'endpoint_citations'
    ]
    
    # Required columns that can have null values
    REQUIRED_NULLABLE_COLUMNS = [
        'measured_dose',
        'measured_concentration',
        'measured_timepoint',
        'measured_death_percentage'
    ]
    
    @classmethod
    def validate_xlsx(cls, xlsx_path: Path, sheet_name: str = "Endpoints") -> pd.DataFrame:
        """
        Validate extraction/test data XLSX file.
        
        Args:
            xlsx_path: Path to XLSX file
            sheet_name: Name of sheet containing extraction data
            
        Returns:
            pd.DataFrame: Validated dataframe
            
        Raises:
            ValueError: If validation fails
            FileNotFoundError: If file or sheet doesn't exist
        """
        logger.info(f"Validating extraction XLSX file: {xlsx_path}")
        
        if not xlsx_path.exists():
            raise FileNotFoundError(f"XLSX file not found: {xlsx_path}")
        
        # Read the XLSX file - use default header row (no multi-level headers expected)
        try:
            df = pd.read_excel(xlsx_path, sheet_name=sheet_name)
            logger.info("Successfully read extraction XLSX file")
        except ValueError as e:
            if "Worksheet named" in str(e):
                # Sheet doesn't exist
                available_sheets = pd.ExcelFile(xlsx_path).sheet_names
                raise ValueError(f"Sheet '{sheet_name}' not found. Available sheets: {available_sheets}")
            raise
        
        # Check for required columns (after processing)
        all_required_columns = cls.REQUIRED_NON_NULL_COLUMNS + cls.REQUIRED_NULLABLE_COLUMNS
        missing_columns = set(all_required_columns) - set(df.columns)
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Check for non-null requirements
        for col in cls.REQUIRED_NON_NULL_COLUMNS:
            null_count = df[col].isnull().sum()
            if null_count > 0:
                logger.warning(f"Column '{col}' has {null_count} null values")
        
        logger.info(f"Validation successful. Data shape: {df.shape}")
        return df
    
    @classmethod
    def validate_raw_extraction(cls, xlsx_path: Path, sheet_name: str = "Endpoints") -> pd.DataFrame:
        """
        Validate raw extraction XLSX file (before processing).
        
        Args:
            xlsx_path: Path to raw extraction XLSX file
            sheet_name: Name of sheet containing extraction data
            
        Returns:
            pd.DataFrame: Validated raw dataframe
            
        Raises:
            ValueError: If validation fails
        """
        logger.info(f"Validating raw extraction XLSX file: {xlsx_path}")
        
        if not xlsx_path.exists():
            raise FileNotFoundError(f"XLSX file not found: {xlsx_path}")
        
        # Read the raw XLSX file
        try:
            df = pd.read_excel(xlsx_path, sheet_name=sheet_name)
            logger.info("Successfully read raw extraction XLSX file")
        except ValueError as e:
            if "Worksheet named" in str(e):
                available_sheets = pd.ExcelFile(xlsx_path).sheet_names
                raise ValueError(f"Sheet '{sheet_name}' not found. Available sheets: {available_sheets}")
            raise
        
        # Expected raw columns (before processing)
        expected_raw_columns = [
            'id',
            'adc_name',
            'model_name', 
            'model_type',
            'experiment_type',
            'endpoint_name',
            'measured_value',
            'endpoint_citations'
        ]
        
        missing_columns = set(expected_raw_columns) - set(df.columns)
        if missing_columns:
            raise ValueError(f"Missing required raw columns: {missing_columns}")
        
        logger.info(f"Raw validation successful. Data shape: {df.shape}")
        return df