#!/usr/bin/env python3
"""
Test Data Preparation Script

Processes test/extraction data XLSX files for ADC evaluation.
Handles XLSX files with 'Endpoints' sheet containing extraction results.
"""

import argparse
import logging
import shutil
import sys
from datetime import datetime
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any

import pandas as pd

# Add the src directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from config.paths import get_evaluations_path, ensure_path_exists
from preprocessing.extraction_validator import ExtractionValidator


def setup_logging(log_file_path: Path) -> logging.Logger:
    """
    Setup logging to write to both console and file.
    
    Args:
        log_file_path: Path to log file
        
    Returns:
        Configured logger
    """
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    file_handler = logging.FileHandler(log_file_path)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


def generate_timestamp() -> str:
    """Generate timestamp in YYYYMMDD_HHMMSS format."""
    return datetime.now().strftime("%Y%m%d_%H%M%S")


def process_extraction_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Process raw extraction dataframe.
    
    Args:
        df: Raw extraction dataframe
        
    Returns:
        pd.DataFrame: Processed dataframe with proper column names and extraction_id
    """
    # Define the columns we need for evaluation (in raw format)
    required_columns = [
        'id',
        'adc_name',
        'model_name',
        'model_type', 
        'experiment_type',
        'endpoint_name',
        'measured_value',
        'measured_dose',
        'measured_concentration',
        'measured_timepoint',
        'measured_death_percentage',
        'endpoint_citations'
    ]
    
    # Filter to only include required columns
    filtered_df = df[required_columns].copy()
    
    # Column mappings
    column_mappings = {
        'id': 'paper_id',
        'endpoint_citations': 'support_span'
    }
    
    # Apply column mappings
    processed_df = filtered_df.rename(columns=column_mappings)
    
    # Generate extraction_id as {paper_id}_extraction_{count_within_id}
    extraction_ids = []
    id_counters = defaultdict(int)
    
    for _, row in processed_df.iterrows():
        paper_id = row['paper_id']
        id_counters[paper_id] += 1
        extraction_id = f"{paper_id}_extraction_{id_counters[paper_id]}"
        extraction_ids.append(extraction_id)
    
    # Add extraction_id column
    processed_df['extraction_id'] = extraction_ids
    
    return processed_df


def main():
    """Main function for test data preparation."""
    parser = argparse.ArgumentParser(description="Prepare test/extraction data from XLSX")
    parser.add_argument("xlsx_path", help="Path to test data XLSX file")
    parser.add_argument("--sheet-name", default="Endpoints", 
                       help="Name of sheet containing test data (default: 'Endpoints')")
    parser.add_argument("--output-timestamp", help="Override timestamp for output folder")
    parser.add_argument("--log-file", help="Path to log file (defaults to input folder)")
    
    args = parser.parse_args()
    
    xlsx_path = Path(args.xlsx_path)
    if not xlsx_path.exists():
        print(f"ERROR: XLSX file not found: {xlsx_path}")
        return 1
    
    # Generate timestamp for this processing run
    timestamp = args.output_timestamp or generate_timestamp()
    
    # Create timestamp-based output directory
    evaluations_base = get_evaluations_path()
    output_dir = ensure_path_exists(evaluations_base / timestamp)
    input_dir = ensure_path_exists(output_dir / "input")
    
    # Setup logging
    log_file_path = Path(args.log_file) if args.log_file else input_dir / "test_data_prep.log"
    logger = setup_logging(log_file_path)
    
    logger.info(f"Starting test data preparation")
    logger.info(f"Processing {xlsx_path}")
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Input directory: {input_dir}")
    logger.info(f"Log file: {log_file_path}")
    
    try:
        # Step 1: Copy XLSX to input folder
        raw_extraction_path = input_dir / "raw_extraction.xlsx"
        shutil.copy2(xlsx_path, raw_extraction_path)
        logger.info(f"Copied XLSX to: {raw_extraction_path}")
        
        # Step 2: Validate raw extraction data
        logger.info("Validating raw extraction data...")
        raw_df = ExtractionValidator.validate_raw_extraction(raw_extraction_path, sheet_name=args.sheet_name)
        
        # Step 3: Process extraction data
        logger.info("Processing extraction data...")
        processed_df = process_extraction_data(raw_df)
        
        # Step 4: Validate processed extraction data
        logger.info("Validating processed extraction data...")
        processed_extraction_path = input_dir / "processed_extraction.xlsx"
        processed_df.to_excel(processed_extraction_path, sheet_name="Endpoints", index=False)
        logger.info(f"Processed XLSX saved to: {processed_extraction_path}")
        
        # Final validation of processed data
        ExtractionValidator.validate_xlsx(processed_extraction_path, sheet_name="Endpoints")
        
        # Step 5: Print summary
        total_extractions = len(processed_df)
        unique_papers = processed_df['paper_id'].nunique()
        unique_endpoints = processed_df['endpoint_name'].nunique()
        
        logger.info(f"Processing complete!")
        logger.info(f"Total extractions processed: {total_extractions}")
        logger.info(f"Unique papers: {unique_papers}")
        logger.info(f"Unique endpoints: {unique_endpoints}")
        
        # Print paper summary
        paper_counts = processed_df['paper_id'].value_counts()
        for paper_id, count in paper_counts.head(5).items():
            logger.info(f"  Paper {paper_id}: {count} extractions")
        
        if len(paper_counts) > 5:
            logger.info(f"  ... and {len(paper_counts) - 5} more papers")
        
        # Final log file location
        logger.info(f"Test data preparation completed successfully")
        logger.info(f"Log file saved to: {log_file_path}")
        print(f"\n=== TEST DATA PREPARATION COMPLETE ===")
        print(f"Log file location: {log_file_path}")
        print(f"Evaluation folder: {output_dir}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error processing test data: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())