import pandas as pd
from pathlib import Path
import logging
from typing import Optional


logger = logging.getLogger(__name__)


class AnnotationValidator:
    """Validates training data XLSX files for ADC evaluation."""
    
    # Required columns that must have non-null values
    REQUIRED_NON_NULL_COLUMNS = [
        'paper_id',
        'extraction_id', 
        'adc_name',
        'model_name',
        'model_type',
        'experiment_type',
        'endpoint_name',
        'measured_value',
        'support_span',
        'new_human_classification',
        'human_reasoning'
    ]
    
    # Required columns that can have null values
    REQUIRED_NULLABLE_COLUMNS = [
        'measured_dose',
        'measured_concentration',
        'measured_timepoint',
        'measured_death_percentage'
    ]
    
    @classmethod
    def validate_xlsx(cls, xlsx_path: Path, sheet_name: str = "Few-shot examples") -> pd.DataFrame:
        """
        Validate training data XLSX file.
        
        Args:
            xlsx_path: Path to XLSX file
            sheet_name: Name of sheet containing training data
            
        Returns:
            pd.DataFrame: Validated dataframe
            
        Raises:
            ValueError: If validation fails
            FileNotFoundError: If file or sheet doesn't exist
        """
        logger.info(f"Validating XLSX file: {xlsx_path}")
        
        if not xlsx_path.exists():
            raise FileNotFoundError(f"XLSX file not found: {xlsx_path}")
        
        # Read the XLSX file - skip first row (parent headers), use second row as column names
        try:
            df = pd.read_excel(xlsx_path, sheet_name=sheet_name, header=1)
            logger.info("Successfully read XLSX file (using row 2 as header)")
        except ValueError as e:
            if "Worksheet named" in str(e):
                # Sheet doesn't exist
                available_sheets = pd.ExcelFile(xlsx_path).sheet_names
                raise ValueError(f"Sheet '{sheet_name}' not found. Available sheets: {available_sheets}")
            raise
        
        # Check for required columns
        all_required_columns = cls.REQUIRED_NON_NULL_COLUMNS + cls.REQUIRED_NULLABLE_COLUMNS
        missing_columns = set(all_required_columns) - set(df.columns)
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Check for non-null requirements
        for col in cls.REQUIRED_NON_NULL_COLUMNS:
            null_count = df[col].isnull().sum()
            if null_count > 0:
                logger.warning(f"Column '{col}' has {null_count} null values")
        
        # Handle column name mapping if needed (citation -> support_span)
        if 'citation' in df.columns and 'support_span' not in df.columns:
            df = df.rename(columns={'citation': 'support_span'})
            logger.info("Mapped 'citation' column to 'support_span'")
        
        logger.info(f"Validation successful. Data shape: {df.shape}")
        return df
    
    @classmethod
    def validate_classification_values(cls, df: pd.DataFrame) -> None:
        """
        Validate that classification values are TP or FP.
        
        Args:
            df: Dataframe to validate
            
        Raises:
            ValueError: If invalid classification values found
        """
        valid_classifications = {'TP', 'FP'}
        invalid_values = set(df['new_human_classification'].dropna()) - valid_classifications
        
        if invalid_values:
            raise ValueError(f"Invalid classification values found: {invalid_values}. Must be TP or FP.")