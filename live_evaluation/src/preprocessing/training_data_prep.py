#!/usr/bin/env python3
"""
Training Data Preparation Script

Converts training data XLSX files to structured JSON few-shot examples.
Handles XLSX files with 'Few-shot examples' sheet containing human annotations.
"""

import argparse
import json
import logging
import shutil
import sys
from datetime import datetime
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any

import pandas as pd

# Add the src directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from config.paths import get_annotations_path, ensure_path_exists
from preprocessing.annotation_validator import AnnotationValidator


def setup_logging(log_file_path: Path) -> logging.Logger:
    """
    Setup logging to write to both console and file.
    
    Args:
        log_file_path: Path to log file
        
    Returns:
        Configured logger
    """
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    file_handler = logging.FileHandler(log_file_path)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


def generate_timestamp() -> str:
    """Generate timestamp in YYYYMMDD_HHMMSS format."""
    return datetime.now().strftime("%Y%m%d_%H%M%S")


def convert_to_structured_json(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Convert validated dataframe to structured JSON format for few-shot examples.
    
    Groups examples by endpoint_name, then by TP/FP classification.
    
    Args:
        df: Validated training data dataframe
        
    Returns:
        Dict containing structured few-shot examples
    """
    structured_data = defaultdict(lambda: {"tp_examples": [], "fp_examples": []})
    
    for _, row in df.iterrows():
        endpoint = row['endpoint_name']
        classification = row['new_human_classification']
        
        example = {
            "extraction_id": row['extraction_id'],
            "paper_id": row['paper_id'],
            "adc_name": row['adc_name'],
            "model_name": row['model_name'],
            "model_type": row['model_type'],
            "experiment_type": row['experiment_type'],
            "endpoint_name": row['endpoint_name'],
            "measured_value": row['measured_value'],
            "measured_dose": row.get('measured_dose'),
            "measured_concentration": row.get('measured_concentration'),
            "measured_timepoint": row.get('measured_timepoint'),
            "measured_death_percentage": row.get('measured_death_percentage'),
            "support_span": row['support_span'],
            "classification": classification,
            "reasoning": row['human_reasoning']
        }
        
        # Add to appropriate classification list
        if classification == "TP":
            structured_data[endpoint]["tp_examples"].append(example)
        elif classification == "FP":
            structured_data[endpoint]["fp_examples"].append(example)
        else:
            logger.warning(f"Unknown classification '{classification}' for extraction_id {row['extraction_id']}")
    
    # Convert defaultdict to regular dict
    return dict(structured_data)


def main():
    """Main function for training data preparation."""
    parser = argparse.ArgumentParser(description="Prepare training data from XLSX annotations")
    parser.add_argument("xlsx_path", help="Path to raw annotations XLSX file")
    parser.add_argument("--sheet-name", default="Few-shot examples", 
                       help="Name of sheet containing training data (default: 'Few-shot examples')")
    parser.add_argument("--output-timestamp", help="Override timestamp for output folder")
    parser.add_argument("--log-file", help="Path to log file (defaults to timestamp folder)")
    
    args = parser.parse_args()
    
    xlsx_path = Path(args.xlsx_path)
    if not xlsx_path.exists():
        print(f"ERROR: XLSX file not found: {xlsx_path}")
        return 1
    
    # Generate timestamp for this processing run
    timestamp = args.output_timestamp or generate_timestamp()
    
    # Create timestamp-based output directory
    annotations_base = get_annotations_path()
    output_dir = ensure_path_exists(annotations_base / timestamp)
    
    # Setup logging
    log_file_path = Path(args.log_file) if args.log_file else output_dir / "training_data_prep.log"
    logger = setup_logging(log_file_path)
    
    logger.info(f"Starting training data preparation")
    logger.info(f"Processing {xlsx_path}")
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Log file: {log_file_path}")
    
    try:
        # Step 1: Copy XLSX to timestamped folder
        raw_annotations_path = output_dir / "raw_annotations.xlsx"
        shutil.copy2(xlsx_path, raw_annotations_path)
        logger.info(f"Copied XLSX to: {raw_annotations_path}")
        
        # Step 2: Validate the XLSX file
        logger.info("Validating annotations...")
        df = AnnotationValidator.validate_xlsx(raw_annotations_path, sheet_name=args.sheet_name)
        AnnotationValidator.validate_classification_values(df)
        
        # Step 3: Convert to structured JSON
        logger.info("Converting to structured JSON...")
        structured_data = convert_to_structured_json(df)
        
        # Step 4: Save structured JSON
        json_output_path = output_dir / "structured_annotations.json"
        with open(json_output_path, 'w', encoding='utf-8') as f:
            json.dump(structured_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Structured JSON saved to: {json_output_path}")
        
        # Step 5: Print summary
        total_examples = sum(len(endpoint_data["tp_examples"]) + len(endpoint_data["fp_examples"]) 
                           for endpoint_data in structured_data.values())
        total_endpoints = len(structured_data)
        
        logger.info(f"Processing complete!")
        logger.info(f"Total examples processed: {total_examples}")
        logger.info(f"Total unique endpoints: {total_endpoints}")
        
        # Print endpoint summary
        for endpoint, data in structured_data.items():
            tp_count = len(data["tp_examples"])
            fp_count = len(data["fp_examples"])
            logger.info(f"  {endpoint}: {tp_count} TP, {fp_count} FP")
        
        # Final log file location
        logger.info(f"Training data preparation completed successfully")
        logger.info(f"Log file saved to: {log_file_path}")
        print(f"\n=== TRAINING DATA PREPARATION COMPLETE ===")
        print(f"Log file location: {log_file_path}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error processing training data: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())