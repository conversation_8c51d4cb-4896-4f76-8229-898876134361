"""
XLSX converter for evaluation results.

This module converts full_results.json to Excel format for easier analysis
and sharing with domain experts.
"""

import json
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class XLSXConverter:
    """
    Converts evaluation results from JSON to XLSX format.
    
    This class handles the conversion of full_results.json to results.xlsx,
    maintaining all fields and data integrity from the JSON structure.
    """
    
    def __init__(self, output_dir: Path):
        """
        Initialize the XLSX converter.
        
        Args:
            output_dir: Path to evaluation output directory containing full_results.json
        """
        self.output_dir = Path(output_dir)
        self.full_results_path = self.output_dir / "full_results.json"
        self.results_xlsx_path = self.output_dir / "results.xlsx"
        
        logger.info(f"Initialized XLSXConverter with output directory: {output_dir}")
    
    def load_results(self) -> Dict[str, Any]:
        """
        Load evaluation results from full_results.json.
        
        Returns:
            Dictionary containing metadata and results
            
        Raises:
            FileNotFoundError: If full_results.json doesn't exist
            json.JSONDecodeError: If file contains invalid JSON
        """
        if not self.full_results_path.exists():
            raise FileNotFoundError(f"Results file not found: {self.full_results_path}")
        
        try:
            with open(self.full_results_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"Loaded {len(data.get('results', []))} results from: {self.full_results_path}")
            return data
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in results file: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading results: {e}")
            raise
    
    def convert_to_xlsx(self) -> Path:
        """
        Convert full_results.json to results.xlsx.
        
        Returns:
            Path to created XLSX file
            
        Raises:
            ValueError: If results data is invalid or empty
        """
        try:
            # Load results data
            data = self.load_results()
            results = data.get("results", [])
            
            if not results:
                raise ValueError("No results found in full_results.json")
            
            # Convert to DataFrame
            df = pd.DataFrame(results)
            
            # Ensure consistent column ordering (important fields first)
            preferred_columns = [
                'paper_id', 'extraction_id', 'adc_name', 'model_name', 'model_type',
                'experiment_type', 'endpoint_name', 'measured_value', 'measured_dose',
                'measured_concentration', 'measured_timepoint', 'measured_death_percentage',
                'support_span', 'predicted_class', 'reasoning', 'status', 'evaluation_timestamp'
            ]
            
            # Reorder columns: preferred first, then any remaining
            available_preferred = [col for col in preferred_columns if col in df.columns]
            remaining_columns = [col for col in df.columns if col not in preferred_columns]
            final_columns = available_preferred + remaining_columns
            df = df[final_columns]
            
            # Save to Excel
            with pd.ExcelWriter(self.results_xlsx_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Results', index=False)
                
                # Auto-adjust column widths for better readability
                worksheet = writer.sheets['Results']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    # Set column width (max 50 characters for readability)
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"Successfully converted {len(results)} results to: {self.results_xlsx_path}")
            return self.results_xlsx_path
            
        except Exception as e:
            logger.error(f"Error converting to XLSX: {e}")
            raise


def convert_results_to_xlsx(output_dir: Path) -> Path:
    """
    Convert evaluation results from JSON to XLSX format.
    
    Args:
        output_dir: Path to evaluation output directory
        
    Returns:
        Path to created XLSX file
    """
    converter = XLSXConverter(output_dir)
    return converter.convert_to_xlsx()


if __name__ == "__main__":
    # For testing purposes
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python xlsx_converter.py <output_directory>")
        sys.exit(1)
    
    output_dir = Path(sys.argv[1])
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        xlsx_path = convert_results_to_xlsx(output_dir)
        print(f"Successfully created: {xlsx_path}")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)