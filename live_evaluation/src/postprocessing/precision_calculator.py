"""
Precision calculator for evaluation results.

This module calculates precision metrics at endpoint and paper levels,
excluding failed evaluations and providing detailed breakdowns.
"""

import json
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Tuple
from collections import defaultdict

logger = logging.getLogger(__name__)


class PrecisionCalculator:
    """
    Calculates precision metrics from evaluation results.
    
    This class computes per-endpoint and per-paper precision scores
    based on TP/FP predictions, excluding FAILED status evaluations.
    """
    
    def __init__(self, output_dir: Path):
        """
        Initialize the precision calculator.
        
        Args:
            output_dir: Path to evaluation output directory containing full_results.json
        """
        self.output_dir = Path(output_dir)
        self.full_results_path = self.output_dir / "full_results.json"
        self.endpoint_precision_path = self.output_dir / "endpoint_precision.xlsx"
        self.paper_precision_path = self.output_dir / "paper_precision.xlsx"
        
        logger.info(f"Initialized PrecisionCalculator with output directory: {output_dir}")
    
    def load_results(self) -> Dict[str, Any]:
        """
        Load evaluation results from full_results.json.
        
        Returns:
            Dictionary containing metadata and results
            
        Raises:
            FileNotFoundError: If full_results.json doesn't exist
            json.JSONDecodeError: If file contains invalid JSON
        """
        if not self.full_results_path.exists():
            raise FileNotFoundError(f"Results file not found: {self.full_results_path}")
        
        try:
            with open(self.full_results_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"Loaded {len(data.get('results', []))} results from: {self.full_results_path}")
            return data
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in results file: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading results: {e}")
            raise
    
    def filter_successful_results(self, results: list) -> list:
        """
        Filter out FAILED status evaluations.
        
        Args:
            results: List of evaluation result dictionaries
            
        Returns:
            List of successful evaluations only
        """
        successful = [r for r in results if r.get("status") == "SUCCESS"]
        failed_count = len(results) - len(successful)
        
        logger.info(f"Filtered results: {len(successful)} successful, {failed_count} failed")
        return successful
    
    def calculate_endpoint_precision(self, results: list) -> pd.DataFrame:
        """
        Calculate precision metrics per endpoint.
        
        Args:
            results: List of successful evaluation result dictionaries
            
        Returns:
            DataFrame with endpoint precision metrics
        """
        endpoint_stats = defaultdict(lambda: {"total": 0, "tp": 0})
        
        # Count TP and total per endpoint
        for result in results:
            endpoint = result.get("endpoint_name", "UNKNOWN")
            predicted_class = result.get("predicted_class", "")
            
            endpoint_stats[endpoint]["total"] += 1
            if predicted_class == "TP":
                endpoint_stats[endpoint]["tp"] += 1
        
        # Convert to DataFrame
        data = []
        for endpoint, stats in endpoint_stats.items():
            precision = stats["tp"] / stats["total"] if stats["total"] > 0 else 0.0
            data.append({
                "endpoint_name": endpoint,
                "total_evaluations": stats["total"],
                "tp_count": stats["tp"],
                "fp_count": stats["total"] - stats["tp"],
                "precision": precision
            })
        
        # Sort by endpoint name for consistency
        df = pd.DataFrame(data).sort_values("endpoint_name").reset_index(drop=True)
        
        logger.info(f"Calculated precision for {len(df)} unique endpoints")
        return df
    
    def calculate_paper_precision(self, results: list) -> pd.DataFrame:
        """
        Calculate precision metrics per paper.
        
        Args:
            results: List of successful evaluation result dictionaries
            
        Returns:
            DataFrame with paper precision metrics
        """
        paper_stats = defaultdict(lambda: {"total": 0, "tp": 0})
        
        # Count TP and total per paper
        for result in results:
            paper_id = result.get("paper_id", "UNKNOWN")
            predicted_class = result.get("predicted_class", "")
            
            paper_stats[paper_id]["total"] += 1
            if predicted_class == "TP":
                paper_stats[paper_id]["tp"] += 1
        
        # Convert to DataFrame
        data = []
        for paper_id, stats in paper_stats.items():
            precision = stats["tp"] / stats["total"] if stats["total"] > 0 else 0.0
            data.append({
                "paper_id": paper_id,
                "total_evaluations": stats["total"],
                "tp_count": stats["tp"],
                "fp_count": stats["total"] - stats["tp"],
                "precision": precision
            })
        
        # Sort by paper ID for consistency
        df = pd.DataFrame(data).sort_values("paper_id").reset_index(drop=True)
        
        logger.info(f"Calculated precision for {len(df)} unique papers")
        return df
    
    def save_precision_xlsx(self, df: pd.DataFrame, output_path: Path, sheet_name: str) -> None:
        """
        Save precision DataFrame to XLSX file.
        
        Args:
            df: DataFrame containing precision metrics
            output_path: Path where XLSX file will be saved
            sheet_name: Name of the Excel sheet
        """
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # Auto-adjust column widths
                worksheet = writer.sheets[sheet_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    # Set column width (minimum 12 characters)
                    adjusted_width = max(max_length + 2, 12)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"Saved {len(df)} precision records to: {output_path}")
            
        except Exception as e:
            logger.error(f"Error saving precision XLSX to {output_path}: {e}")
            raise
    
    def calculate_and_save_precision(self) -> Tuple[Path, Path]:
        """
        Calculate precision metrics and save to XLSX files.
        
        Returns:
            Tuple of (endpoint_precision_path, paper_precision_path)
            
        Raises:
            ValueError: If no successful results found
        """
        try:
            # Load and filter results
            data = self.load_results()
            all_results = data.get("results", [])
            
            if not all_results:
                raise ValueError("No results found in full_results.json")
            
            successful_results = self.filter_successful_results(all_results)
            
            if not successful_results:
                raise ValueError("No successful evaluations found - all results have FAILED status")
            
            # Calculate endpoint precision
            endpoint_df = self.calculate_endpoint_precision(successful_results)
            self.save_precision_xlsx(endpoint_df, self.endpoint_precision_path, "Endpoint_Precision")
            
            # Calculate paper precision
            paper_df = self.calculate_paper_precision(successful_results)
            self.save_precision_xlsx(paper_df, self.paper_precision_path, "Paper_Precision")
            
            # Log summary statistics
            overall_tp = sum(1 for r in successful_results if r.get("predicted_class") == "TP")
            overall_precision = overall_tp / len(successful_results)
            
            logger.info("=== PRECISION SUMMARY ===")
            logger.info(f"Total successful evaluations: {len(successful_results)}")
            logger.info(f"Overall TP predictions: {overall_tp}")
            logger.info(f"Overall FP predictions: {len(successful_results) - overall_tp}")
            logger.info(f"Overall precision: {overall_precision:.3f}")
            logger.info(f"Unique endpoints: {len(endpoint_df)}")
            logger.info(f"Unique papers: {len(paper_df)}")
            
            return self.endpoint_precision_path, self.paper_precision_path
            
        except Exception as e:
            logger.error(f"Error calculating precision: {e}")
            raise


def calculate_precision_metrics(output_dir: Path) -> Tuple[Path, Path]:
    """
    Calculate precision metrics and save to XLSX files.
    
    Args:
        output_dir: Path to evaluation output directory
        
    Returns:
        Tuple of (endpoint_precision_path, paper_precision_path)
    """
    calculator = PrecisionCalculator(output_dir)
    return calculator.calculate_and_save_precision()


if __name__ == "__main__":
    # For testing purposes
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python precision_calculator.py <output_directory>")
        sys.exit(1)
    
    output_dir = Path(sys.argv[1])
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        endpoint_path, paper_path = calculate_precision_metrics(output_dir)
        print(f"Successfully created:")
        print(f"  {endpoint_path}")
        print(f"  {paper_path}")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)