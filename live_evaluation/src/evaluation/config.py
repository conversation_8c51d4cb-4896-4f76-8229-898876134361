#!/usr/bin/env python3
"""
ADC Live Evaluator - Configuration Management

Handles loading training data, fallback example selection, resume logic,
and config.json generation for evaluation runs.
"""

import json
import random
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

# Add the src directory to path for imports
import sys
sys.path.append(str(Path(__file__).parent.parent))
from config.paths import get_annotations_path


logger = logging.getLogger(__name__)


def load_latest_annotations() -> <PERSON>ple[Dict[str, Any], str]:
    """
    Load the most recent structured annotations JSON from data/annotations/.
    
    Returns:
        Tuple[Dict[str, Any], str]: (annotations_data, timestamp)
        
    Raises:
        FileNotFoundError: If no annotations found
        ValueError: If JSON is invalid
    """
    annotations_path = get_annotations_path()
    
    if not annotations_path.exists():
        raise FileNotFoundError(f"Annotations directory not found: {annotations_path}")
    
    # Find all timestamp directories
    timestamp_dirs = []
    for item in annotations_path.iterdir():
        if item.is_dir() and item.name.replace('_', '').isdigit():
            timestamp_dirs.append(item)
    
    if not timestamp_dirs:
        raise FileNotFoundError("No annotation timestamp directories found")
    
    # Sort by timestamp (latest first)
    timestamp_dirs.sort(key=lambda x: x.name, reverse=True)
    latest_dir = timestamp_dirs[0]
    
    # Load structured_annotations.json from latest directory
    annotations_file = latest_dir / "structured_annotations.json"
    if not annotations_file.exists():
        raise FileNotFoundError(f"structured_annotations.json not found in {latest_dir}")
    
    logger.info(f"Loading annotations from: {annotations_file}")
    
    try:
        with open(annotations_file, 'r', encoding='utf-8') as f:
            annotations_data = json.load(f)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in annotations file: {e}")
    
    logger.info(f"Loaded annotations for {len(annotations_data)} endpoints")
    return annotations_data, latest_dir.name


def select_fallback_examples(annotations_data: Dict[str, Any]) -> Dict[str, List[str]]:
    """
    Select 2 random TP and 2 random FP examples for fallback strategy.
    
    Args:
        annotations_data: Structured annotations JSON data
        
    Returns:
        Dict with tp_examples and fp_examples lists containing extraction_ids
        
    Raises:
        ValueError: If insufficient examples available
    """
    all_tp_examples = []
    all_fp_examples = []
    
    # Collect all TP and FP examples from all endpoints
    for endpoint_name, endpoint_data in annotations_data.items():
        tp_examples = endpoint_data.get('tp_examples', [])
        fp_examples = endpoint_data.get('fp_examples', [])
        
        for example in tp_examples:
            all_tp_examples.append(example['extraction_id'])
            
        for example in fp_examples:
            all_fp_examples.append(example['extraction_id'])
    
    logger.info(f"Found {len(all_tp_examples)} total TP examples, {len(all_fp_examples)} total FP examples")
    
    # Validate we have enough examples
    if len(all_tp_examples) < 2:
        raise ValueError(f"Need at least 2 TP examples for fallback, found {len(all_tp_examples)}")
    if len(all_fp_examples) < 2:
        raise ValueError(f"Need at least 2 FP examples for fallback, found {len(all_fp_examples)}")
    
    # Randomly select 2 of each
    selected_tp = random.sample(all_tp_examples, 2)
    selected_fp = random.sample(all_fp_examples, 2)
    
    logger.info(f"Selected fallback examples - TP: {selected_tp}, FP: {selected_fp}")
    
    return {
        "tp_examples": selected_tp,
        "fp_examples": selected_fp
    }


def check_resume_scenario(output_dir: Optional[Path]) -> Tuple[bool, Optional[Path]]:
    """
    Check if this is a resume scenario.
    
    Args:
        output_dir: Optional output directory path from --output-dir argument
        
    Returns:
        Tuple[bool, Optional[Path]]: (is_resume, partial_results_path)
        
    Raises:
        ValueError: If output_dir provided but invalid
    """
    if not output_dir:
        logger.info("No --output-dir provided, starting new evaluation")
        return False, None
    
    output_dir = Path(output_dir)
    
    if not output_dir.exists():
        raise ValueError(f"Output directory does not exist: {output_dir}")
    
    if not output_dir.is_dir():
        raise ValueError(f"Output path is not a directory: {output_dir}")
    
    # Check for partial_results.json in the output subdirectory
    partial_results_path = output_dir / "output" / "partial_results.json"
    
    if partial_results_path.exists():
        logger.info(f"Found partial_results.json, resuming evaluation from: {partial_results_path}")
        return True, partial_results_path
    else:
        # Check if this is a valid evaluation directory structure
        input_dir = output_dir / "input"
        output_subdir = output_dir / "output"
        
        if not (input_dir.exists() and output_subdir.exists()):
            raise ValueError(f"Invalid evaluation directory structure: {output_dir}")
        
        logger.info(f"Valid evaluation directory but no partial_results.json found: {output_dir}")
        raise ValueError("--output-dir should only be used for resuming incomplete evaluations")


def generate_evaluation_config(
    timestamp: str,
    test_data_path: Path,
    annotations_timestamp: str,
    fallback_examples: Dict[str, List[str]],
    parallel_workers: int = 5,
    is_resume: bool = False,
    last_processed_index: int = 0
) -> Dict[str, Any]:
    """
    Generate evaluation configuration based on INTEGRATION_PLAN.md structure.
    
    Args:
        timestamp: Current evaluation timestamp
        test_data_path: Path to test data XLSX file
        annotations_timestamp: Timestamp of annotations being used
        fallback_examples: Selected fallback examples
        parallel_workers: Number of parallel workers
        is_resume: Whether this is a resume scenario
        last_processed_index: Last processed index for resume
        
    Returns:
        Configuration dictionary
    """
    config = {
        "timestamp": timestamp,
        "model_version": "gpt-4.1",
        "temperature": 0,
        "parallel_workers": parallel_workers,
        "checkpoint_interval": 100,
        "annotation_version": annotations_timestamp,
        "test_data_version": timestamp,
        "fallback_examples": fallback_examples,
        "retry_strategy": {
            "delays": [1, 3, 9, 27, 60],
            "max_attempts": 5
        },
        "resume_info": {
            "is_resume": is_resume,
            "last_processed_index": last_processed_index
        }
    }
    
    logger.info(f"Generated configuration for timestamp: {timestamp}")
    logger.info(f"Using annotations from: {annotations_timestamp}")
    logger.info(f"Parallel workers: {parallel_workers}")
    logger.info(f"Resume mode: {is_resume}")
    
    return config


def save_evaluation_config(config: Dict[str, Any], config_path: Path) -> None:
    """
    Save evaluation configuration to config.json file.
    
    Args:
        config: Configuration dictionary
        config_path: Path where to save config.json
    """
    config_path = Path(config_path)
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Saved evaluation config to: {config_path}")


def load_evaluation_config(config_path: Path) -> Dict[str, Any]:
    """
    Load evaluation configuration from config.json file.
    
    Args:
        config_path: Path to config.json file
        
    Returns:
        Configuration dictionary
        
    Raises:
        FileNotFoundError: If config file doesn't exist
        ValueError: If JSON is invalid
    """
    config_path = Path(config_path)
    
    if not config_path.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in config file: {e}")
    
    logger.info(f"Loaded evaluation config from: {config_path}")
    return config