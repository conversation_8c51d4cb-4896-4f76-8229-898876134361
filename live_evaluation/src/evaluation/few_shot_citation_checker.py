"""
Few-shot citation checker for ADC endpoint evaluation.

This module handles the GPT-4.1 API integration and prompt engineering for 
evaluating whether citations support extracted endpoints using few-shot examples.
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional
from pydantic import BaseModel
from openai import AzureOpenAI, APIError, RateLimitError, APIConnectionError, APITimeoutError
from dotenv import load_dotenv

# Try to load environment variables from multiple possible .env locations
def load_env_file():
    """Load .env file from multiple possible locations."""
    # Priority 1: Custom env file path from environment variable
    custom_env = os.environ.get('AZURE_ENV_FILE')
    if custom_env and Path(custom_env).exists():
        load_dotenv(custom_env)
        return
    
    # Priority 2: Current working directory
    if Path('.env').exists():
        load_dotenv('.env')
        return
    
    # Priority 3: Live evaluation directory (when called from there)
    live_eval_env = Path(__file__).parent.parent.parent / ".env"
    if live_eval_env.exists():
        load_dotenv(live_eval_env)
        return
    
    # Priority 4: Parent directories (up to 3 levels up from cwd)
    current = Path.cwd()
    for _ in range(3):
        current = current.parent
        env_path = current / '.env'
        if env_path.exists():
            load_dotenv(env_path)
            return
    
    # If no .env found, continue without it (rely on environment variables)
    logger = logging.getLogger(__name__)
    logger.debug("No .env file found, using environment variables directly")

load_env_file()

logger = logging.getLogger(__name__)


class CitationCheckResult(BaseModel):
    """Structured response format for citation evaluation."""
    classification: str  # "TP" or "FP"
    reasoning: str      # Explanation for the classification


class FewShotCitationChecker:
    """
    Handles few-shot citation checking using Azure OpenAI GPT-4.1.
    
    This class constructs prompts with few-shot examples and evaluates whether
    citations adequately support extracted endpoint information.
    """
    
    def __init__(self, model_name: str = "gpt-4.1"):
        """
        Initialize the citation checker.
        
        Args:
            model_name: Azure OpenAI model deployment name
        """
        self.model = model_name
        self.client = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-08-01-preview"), 
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
        )
        logger.info(f"Initialized FewShotCitationChecker with model: {model_name}")
    
    def build_prompt_with_examples(self, extraction: Dict, few_shot_examples: List[Dict] = None):
        """Build prompt with few-shot examples using system/user message structure."""
        
        # System prompt
        system_prompt = """You are evaluating whether a citation supports an extracted endpoint from an ADC research paper.

Your task is to determine if the citation text provides sufficient evidence to support the extracted endpoint information.

Classification Guidelines:
- TP (True Positive): The citation clearly and directly supports all aspects of the endpoint
- FP (False Positive): The citation does not adequately support the endpoint, or there are discrepancies

Consider:
1. Does the citation mention the specific ADC, model, and endpoint?
2. Are the measured values consistent between extraction and citation?
3. Are the experimental conditions (model_type, experiment_type) accurately reflected?
4. Is the endpoint_name appropriately assigned based on what was actually measured?"""

        # Add few-shot examples if provided
        examples_section = ""
        if few_shot_examples:
            examples_section = "\n\nExamples:\n"
            for i, example in enumerate(few_shot_examples, 1):
                # The example IS the extraction data (no nested 'extraction' key)
                
                # Build example info with conditional fields
                example_info = []
                example_info.append(f"ADC_Name: {example['adc_name']}")
                example_info.append(f"Model_Name: {example['model_name']}")
                example_info.append(f"Model_Type: {example['model_type']}")
                example_info.append(f"Experiment_Type: {example['experiment_type']}")
                example_info.append(f"Endpoint_Name: {example['endpoint_name']}")
                example_info.append(f"Measured_Value: {example['measured_value']}")
                
                # Add conditional measurement fields only if not empty
                if example.get('measured_dose', ''):
                    example_info.append(f"Measured_Dose: {example['measured_dose']}")
                if example.get('measured_concentration', ''):
                    example_info.append(f"Measured_Concentration: {example['measured_concentration']}")
                if example.get('measured_timepoint', ''):
                    example_info.append(f"Measured_Timepoint: {example['measured_timepoint']}")
                if example.get('measured_death_percentage', ''):
                    example_info.append(f"Measured_Death_Percentage: {example['measured_death_percentage']}")
                
                # NO TEXT TRUNCATION - use complete citations and reasoning
                examples_section += f"""
Example {i}:
{chr(10).join(example_info)}

Citation: {example['support_span']}

Classification: {example['classification']}
Reasoning: {example['reasoning']}
"""
        
        # Current evaluation task
        current_info = []
        current_info.append(f"- ADC_Name: {extraction['adc_name']}")
        current_info.append(f"- Model_Name: {extraction['model_name']}")
        current_info.append(f"- Model_Type: {extraction['model_type']}")
        current_info.append(f"- Experiment_Type: {extraction['experiment_type']}")
        current_info.append(f"- Endpoint_Name: {extraction['endpoint_name']}")
        current_info.append(f"- Measured_Value: {extraction['measured_value']}")
        
        # Add conditional measurement fields only if not empty
        if extraction.get('measured_dose', ''):
            current_info.append(f"- Measured_Dose: {extraction['measured_dose']}")
        if extraction.get('measured_concentration', ''):
            current_info.append(f"- Measured_Concentration: {extraction['measured_concentration']}")
        if extraction.get('measured_timepoint', ''):
            current_info.append(f"- Measured_Timepoint: {extraction['measured_timepoint']}")
        if extraction.get('measured_death_percentage', ''):
            current_info.append(f"- Measured_Death_Percentage: {extraction['measured_death_percentage']}")
        
        task_section = f"""
{examples_section}

Now evaluate this extraction:

Endpoint Information:
{chr(10).join(current_info)}

Citation Text:
{extraction['support_span']}

Classify as TP or FP and provide your reasoning."""
        
        return system_prompt, task_section
    
    def check_citation(self, extraction: Dict, few_shot_examples: List[Dict] = None) -> CitationCheckResult:
        """
        Check citation with optional few-shot examples.
        
        Args:
            extraction: Dictionary containing extraction data
            few_shot_examples: List of example evaluations for few-shot prompting
            
        Returns:
            CitationCheckResult with classification and reasoning
        """
        system_prompt, task_section = self.build_prompt_with_examples(extraction, few_shot_examples)
        
        try:
            response = self.client.beta.chat.completions.parse(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": task_section}
                ],
                response_format=CitationCheckResult,
                temperature=0,
                timeout=30
            )
            
            result = response.choices[0].message.parsed
            logger.debug(f"Classification for {extraction.get('id', 'unknown')}: {result.classification}")
            return result
            
        except (RateLimitError, APIError, APIConnectionError, APITimeoutError):
            # Let API-related exceptions bubble up to evaluator for retry handling
            raise
        except Exception as e:
            # Only catch non-retryable exceptions (parsing errors, etc.)
            error_msg = f"Error during processing: {str(e)}"
            logger.error(f"Error checking citation for {extraction.get('id', 'unknown')}: {error_msg}")
            return CitationCheckResult(
                classification="ERROR",
                reasoning=error_msg
            )