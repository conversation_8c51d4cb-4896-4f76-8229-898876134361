"""
Results management and checkpointing for evaluation pipeline.

This module handles saving, loading, and managing evaluation results with
checkpoint functionality for resuming interrupted evaluations.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import pandas as pd

logger = logging.getLogger(__name__)


class ResultsSaver:
    """
    Manages evaluation results with checkpointing capability.
    
    This class handles saving partial results during evaluation and
    managing the transition from partial to full results upon completion.
    """
    
    def __init__(self, output_dir: Path):
        """
        Initialize the results saver.
        
        Args:
            output_dir: Directory where results will be saved
        """
        self.output_dir = Path(output_dir)
        self.partial_results_path = self.output_dir / "partial_results.json"
        self.full_results_path = self.output_dir / "full_results.json"
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Initialized ResultsSaver with output directory: {output_dir}")
    
    def save_partial_results(self, results: List[Dict[str, Any]], checkpoint_info: Dict[str, Any]) -> None:
        """
        Save partial results as checkpoint.
        
        Args:
            results: List of evaluation results
            checkpoint_info: Metadata about the checkpoint (e.g., processed_count, total_count)
        """
        try:
            # Create results structure matching INTEGRATION_PLAN.md format
            results_data = {
                "metadata": {
                    "checkpoint_timestamp": datetime.now().isoformat(),
                    "total_extractions": checkpoint_info.get("total_count", len(results)),
                    "processed_extractions": checkpoint_info.get("processed_count", len(results)),
                    "status": "in_progress"
                },
                "results": results
            }
            
            # Save to partial_results.json
            with open(self.partial_results_path, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved {len(results)} results to checkpoint: {self.partial_results_path}")
            
        except Exception as e:
            logger.error(f"Error saving partial results: {e}")
            raise
    
    def load_partial_results(self) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Load existing partial results for resume.
        
        Returns:
            Tuple[List[Dict], Dict]: (results_list, checkpoint_info)
            
        Raises:
            FileNotFoundError: If partial results file doesn't exist
            json.JSONDecodeError: If file contains invalid JSON
        """
        if not self.partial_results_path.exists():
            raise FileNotFoundError(f"Partial results file not found: {self.partial_results_path}")
        
        try:
            with open(self.partial_results_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            results = data.get("results", [])
            checkpoint_info = data.get("metadata", {})
            
            logger.info(f"Loaded {len(results)} results from checkpoint: {self.partial_results_path}")
            return results, checkpoint_info
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in partial results file: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading partial results: {e}")
            raise
    
    def finalize_results(self, final_results: List[Dict[str, Any]], 
                        total_processed: int, failed_count: int = 0) -> None:
        """
        Finalize results and rename partial to full results.
        
        Args:
            final_results: Complete list of evaluation results
            total_processed: Total number of extractions processed
            failed_count: Number of failed evaluations
        """
        try:
            # Create final results structure
            results_data = {
                "metadata": {
                    "completion_timestamp": datetime.now().isoformat(),
                    "total_extractions": total_processed,
                    "successful_evaluations": len([r for r in final_results if r.get("status") != "FAILED"]),
                    "failed_evaluations": failed_count,
                    "status": "completed"
                },
                "results": final_results
            }
            
            # Save to full_results.json
            with open(self.full_results_path, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, indent=2, ensure_ascii=False)
            
            # Remove partial_results.json if it exists
            if self.partial_results_path.exists():
                self.partial_results_path.unlink()
                logger.info(f"Removed partial results file: {self.partial_results_path}")
            
            logger.info(f"Finalized {len(final_results)} results to: {self.full_results_path}")
            
        except Exception as e:
            logger.error(f"Error finalizing results: {e}")
            raise
    
    def create_evaluation_result(self, extraction_data: Dict[str, Any],
                               predicted_class: str, reasoning: str, status: str = "SUCCESS") -> Dict[str, Any]:
        """
        Create a standardized evaluation result dictionary including all extraction data.
        
        Args:
            extraction_data: Complete extraction data dictionary from processed file
            predicted_class: Classification result ("TP" or "FP")
            reasoning: Explanation for the classification
            status: Evaluation status ("SUCCESS" or "FAILED")
            
        Returns:
            Dict containing complete extraction data plus evaluation results
        """
        # Start with all extraction data and convert NaN to empty strings
        result = {}
        for key, value in extraction_data.items():
            if pd.isna(value):
                result[key] = ""
            else:
                result[key] = value
        
        # Add evaluation results
        result.update({
            "predicted_class": predicted_class,
            "reasoning": reasoning,
            "status": status,
            "evaluation_timestamp": datetime.now().isoformat()
        })
        
        return result
    
    def has_partial_results(self) -> bool:
        """Check if partial results file exists."""
        return self.partial_results_path.exists()
    
    def get_processed_extraction_ids(self) -> set:
        """
        Get set of extraction IDs that have already been processed.
        
        Returns:
            Set of processed extraction IDs
        """
        if not self.has_partial_results():
            return set()
        
        try:
            results, _ = self.load_partial_results()
            return {result.get("extraction_id") for result in results if result.get("extraction_id")}
        except Exception as e:
            logger.warning(f"Error getting processed extraction IDs: {e}")
            return set()