"""
Main evaluation engine with parallel processing for ADC endpoint evaluation.

This module orchestrates the evaluation pipeline, handling data loading,
parallel processing, API retries, progress tracking, and checkpointing.
"""

import time
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tqdm import tqdm

from .config import load_evaluation_config
from .few_shot_citation_checker import Few<PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>
from .results_saver import ResultsSaver

logger = logging.getLogger(__name__)


class EvaluationEngine:
    """
    Main evaluation engine with parallel processing and checkpointing.
    
    This class handles the complete evaluation workflow including data loading,
    parallel processing with configurable workers, API retry logic, progress 
    tracking, and checkpointing every 100 evaluations.
    """
    
    def __init__(self, evaluation_dir: Path, parallel_workers: int = 5):
        """
        Initialize the evaluation engine.
        
        Args:
            evaluation_dir: Path to evaluation directory containing config and data
            parallel_workers: Number of parallel workers (1-5)
        """
        self.evaluation_dir = Path(evaluation_dir)
        self.parallel_workers = max(1, min(5, parallel_workers))
        
        # Initialize components
        self.output_dir = self.evaluation_dir / "output"
        self.results_saver = ResultsSaver(self.output_dir)
        self.citation_checker = FewShotCitationChecker()
        
        # Load configuration
        config_path = self.output_dir / "config.json"
        self.config = load_evaluation_config(config_path)
        
        # Load full annotations data separately (not stored in config)
        from .config import load_latest_annotations
        self.annotations_data, _ = load_latest_annotations()
        
        # API retry configuration
        self.retry_delays = [1, 3, 9, 27, 60]  # seconds
        self.max_retries = 5
        self.checkpoint_interval = 100
        
        logger.info(f"Initialized EvaluationEngine with {parallel_workers} workers")
        logger.info(f"Evaluation directory: {evaluation_dir}")
    
    def load_test_data(self) -> pd.DataFrame:
        """
        Load processed test data from evaluation directory.
        
        Returns:
            DataFrame containing test extraction data
        """
        test_data_path = self.evaluation_dir / "input" / "processed_extraction.xlsx"
        
        if not test_data_path.exists():
            raise FileNotFoundError(f"Processed test data not found: {test_data_path}")
        
        # Load the Endpoints sheet
        df = pd.read_excel(test_data_path, sheet_name="Endpoints")
        logger.info(f"Loaded {len(df)} extractions from: {test_data_path}")
        
        return df
    
    def get_few_shot_examples_for_endpoint(self, endpoint: str) -> List[Dict]:
        """
        Get few-shot examples for a specific endpoint.
        
        Args:
            endpoint: Endpoint name
            
        Returns:
            List of few-shot examples (endpoint-specific or fallback)
        """
        annotations_data = self.annotations_data
        fallback_examples = self.config.get("fallback_examples", {})
        
        # Check if endpoint has specific training examples
        if endpoint in annotations_data:
            endpoint_examples = annotations_data[endpoint]
            # These are already full example dictionaries
            all_examples = endpoint_examples.get('tp_examples', []) + endpoint_examples.get('fp_examples', [])
            logger.debug(f"Using {len(all_examples)} endpoint-specific examples for: {endpoint}")
            return all_examples
        else:
            # Use fallback examples - these are extraction IDs that need to be resolved
            tp_example_ids = fallback_examples.get("tp_examples", [])
            fp_example_ids = fallback_examples.get("fp_examples", [])
            
            # Resolve IDs to actual examples by searching through all endpoints
            fallback_list = []
            all_example_ids = tp_example_ids + fp_example_ids
            
            for example_id in all_example_ids:
                # Search through all endpoints for this example
                example_found = False
                for endpoint_name, endpoint_data in annotations_data.items():
                    for example_type in ['tp_examples', 'fp_examples']:
                        examples = endpoint_data.get(example_type, [])
                        for example in examples:
                            if example.get('extraction_id') == example_id:
                                fallback_list.append(example)
                                example_found = True
                                break
                        if example_found:
                            break
                    if example_found:
                        break
                
                if not example_found:
                    logger.warning(f"Could not find fallback example with ID: {example_id}")
            
            logger.debug(f"Using {len(fallback_list)} fallback examples for untrained endpoint: {endpoint}")
            return fallback_list
    
    def evaluate_single_extraction(self, extraction_data: Dict) -> Dict[str, Any]:
        """
        Evaluate a single extraction with retry logic.
        
        Args:
            extraction_data: Dictionary containing extraction information
            
        Returns:
            Evaluation result dictionary
        """
        extraction_id = extraction_data.get("extraction_id", "unknown")
        endpoint = extraction_data.get("endpoint_name", "unknown")
        
        # Get few-shot examples for this endpoint
        few_shot_examples = self.get_few_shot_examples_for_endpoint(endpoint)
        
        # Retry logic with exponential backoff
        for attempt in range(self.max_retries):
            try:
                # Call citation checker
                result = self.citation_checker.check_citation(
                    extraction_data, 
                    few_shot_examples
                )
                
                # Create evaluation result with complete extraction data
                status = "FAILED" if result.classification == "ERROR" else "SUCCESS"
                return self.results_saver.create_evaluation_result(
                    extraction_data=extraction_data,
                    predicted_class=result.classification,
                    reasoning=result.reasoning,
                    status=status
                )
                
            except Exception as e:
                wait_time = self.retry_delays[min(attempt, len(self.retry_delays) - 1)]
                logger.warning(f"Attempt {attempt + 1}/{self.max_retries} failed for {extraction_id}: {e}")
                
                if attempt < self.max_retries - 1:
                    logger.info(f"Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    # Final attempt failed, mark as FAILED
                    logger.error(f"All retry attempts failed for {extraction_id}")
                    return self.results_saver.create_evaluation_result(
                        extraction_data=extraction_data,
                        predicted_class="ERROR",  # Default to ERROR for failed evaluations
                        reasoning=f"Error during processing (retry attempt #{attempt + 1}): {str(e)}",
                        status="FAILED"
                    )
    
    def run_evaluation(self, resume: bool = False) -> Dict[str, Any]:
        """
        Run the complete evaluation pipeline.
        
        Args:
            resume: Whether to resume from existing checkpoint
            
        Returns:
            Dictionary containing evaluation summary
        """
        logger.info("Starting evaluation pipeline...")
        
        # Load test data
        test_df = self.load_test_data()
        total_extractions = len(test_df)
        
        # Handle resume scenario
        completed_results = []
        processed_ids = set()
        start_index = 0
        
        if resume and self.results_saver.has_partial_results():
            logger.info("Resuming from checkpoint...")
            completed_results, checkpoint_info = self.results_saver.load_partial_results()
            processed_ids = self.results_saver.get_processed_extraction_ids()
            start_index = len(completed_results)
            logger.info(f"Resuming from {start_index}/{total_extractions} extractions")
        
        # Filter out already processed extractions for resume
        if processed_ids:
            remaining_df = test_df[~test_df['extraction_id'].isin(processed_ids)].copy()
        else:
            remaining_df = test_df.copy()
        
        remaining_count = len(remaining_df)
        logger.info(f"Processing {remaining_count} remaining extractions with {self.parallel_workers} workers")
        
        # Convert DataFrame to list of dictionaries
        extractions_to_process = remaining_df.to_dict('records')
        
        # Initialize progress bar
        progress_bar = tqdm(
            total=total_extractions,
            initial=start_index,
            desc="Evaluating endpoints",
            unit="extractions"
        )
        
        # Parallel processing with ThreadPoolExecutor
        new_results = []
        failed_count = 0
        
        try:
            with ThreadPoolExecutor(max_workers=self.parallel_workers) as executor:
                # Submit all tasks
                future_to_extraction = {
                    executor.submit(self.evaluate_single_extraction, extraction): extraction
                    for extraction in extractions_to_process
                }
                
                # Process completed tasks
                for future in as_completed(future_to_extraction):
                    try:
                        result = future.result()
                        new_results.append(result)
                        
                        if result.get("status") == "FAILED":
                            failed_count += 1
                        
                        progress_bar.update(1)
                        
                        # Checkpoint every 100 evaluations
                        if len(new_results) % self.checkpoint_interval == 0:
                            all_results = completed_results + new_results
                            checkpoint_info = {
                                "total_count": total_extractions,
                                "processed_count": len(all_results)
                            }
                            self.results_saver.save_partial_results(all_results, checkpoint_info)
                            logger.info(f"Checkpoint saved: {len(all_results)}/{total_extractions} completed")
                        
                    except Exception as e:
                        extraction = future_to_extraction[future]
                        extraction_id = extraction.get("extraction_id", "unknown")
                        logger.error(f"Unexpected error processing {extraction_id}: {e}")
                        
                        # Create failed result
                        failed_result = self.results_saver.create_evaluation_result(
                            extraction_data=extraction,
                            predicted_class="ERROR",
                            reasoning=f"Processing error: {str(e)}",
                            status="FAILED"
                        )
                        new_results.append(failed_result)
                        failed_count += 1
                        progress_bar.update(1)
            
        finally:
            progress_bar.close()
        
        # Combine all results
        final_results = completed_results + new_results
        total_processed = len(final_results)
        
        # Calculate summary statistics including ERROR classifications as failures
        error_count = len([r for r in final_results if r.get("predicted_class") == "ERROR"])
        actual_failed_count = failed_count + error_count
        successful_count = total_processed - actual_failed_count
        tp_count = len([r for r in final_results if r.get("predicted_class") == "TP" and r.get("status") == "SUCCESS"])
        fp_count = len([r for r in final_results if r.get("predicted_class") == "FP" and r.get("status") == "SUCCESS"])
        
        # Finalize results with corrected failed count
        self.results_saver.finalize_results(final_results, total_processed, actual_failed_count)
        
        summary = {
            "total_extractions": total_extractions,
            "processed_extractions": total_processed,
            "successful_evaluations": successful_count,
            "failed_evaluations": actual_failed_count,
            "tp_predictions": tp_count,
            "fp_predictions": fp_count,
            "precision": tp_count / successful_count if successful_count > 0 else 0.0
        }
        
        logger.info("Evaluation completed successfully!")
        logger.info(f"Total processed: {total_processed}/{total_extractions}")
        logger.info(f"Successful: {successful_count}, Failed: {actual_failed_count}")
        logger.info(f"TP: {tp_count}, FP: {fp_count}, Precision: {summary['precision']:.3f}")
        
        return summary