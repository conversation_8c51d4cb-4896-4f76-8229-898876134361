#!/bin/bash
# ADC Live Evaluation Integration Script for Production
# This script is called after extraction pipeline completes
 
# Exit on error
set -e
 
echo "========================================"
echo "ADC EXTRACTION AND EVALUATION PIPELINE"
echo "========================================"
 
# Set environment for live evaluation (needed for both batches)
export DOMINO_DATASETS_DIR="/domino/datasets/local/adc_data_center"
 
# Optional: Set custom .env file location if needed
# export AZURE_ENV_FILE="/path/to/production/.env"
 
# ========== BATCH 1 ==========
echo ""
echo "========== PROCESSING BATCH 1 =========="
 
# Step 1: Run extraction pipeline for batch 1
echo "[Batch 1 - 1/3] Running extraction pipeline..."
python src/extraction_pipeline_parallel_strurcture.py --input-dir /domino/datasets/local/adc_data_center/papers_by_type_final_batch_1 --output-dir /domino/datasets/local/adc_data_center/iter_3_final_batch_1
 
# Step 2: Create Excel from extraction results for batch 1
echo "[Batch 1 - 2/3] Creating Excel from extraction results..."
python src/create_excel_from_extraction.py \
    --input-dir /domino/datasets/local/adc_data_center/iter_3_final_batch_1 \
    --output-file /domino/datasets/local/adc_data_center/iter_3_final_batch_1.xlsx
 
# Step 3: Run live evaluation pipeline for batch 1
echo "[Batch 1 - 3/3] Running live evaluation..."
cd live_evaluation
python src/main.py \
    --test-data /domino/datasets/local/adc_data_center/iter_3_final_batch_1.xlsx \
    --parallel-workers 5
cd ..
 
echo "Batch 1 completed successfully!"
 
# ========== BATCH 2 ==========
echo ""
echo "========== PROCESSING BATCH 2 =========="
 
# Step 1: Run extraction pipeline for batch 2
echo "[Batch 2 - 1/3] Running extraction pipeline..."
python src/extraction_pipeline_parallel_strurcture.py --input-dir /domino/datasets/local/adc_data_center/papers_by_type_final_batch_2 --output-dir /domino/datasets/local/adc_data_center/iter_3_final_batch_2
 
# Step 2: Create Excel from extraction results for batch 2
echo "[Batch 2 - 2/3] Creating Excel from extraction results..."
python src/create_excel_from_extraction.py \
    --input-dir /domino/datasets/local/adc_data_center/iter_3_final_batch_2 \
    --output-file /domino/datasets/local/adc_data_center/iter_3_final_batch_2.xlsx
 
# Step 3: Run live evaluation pipeline for batch 2
echo "[Batch 2 - 3/3] Running live evaluation..."
cd live_evaluation
python src/main.py \
    --test-data /domino/datasets/local/adc_data_center/iter_3_final_batch_2.xlsx \
    --parallel-workers 5
cd ..
 
echo "Batch 2 completed successfully!"
 
echo ""
echo "========================================"
echo "ALL BATCHES COMPLETED SUCCESSFULLY"
echo "========================================"
echo "Evaluation results saved to: /domino/datasets/local/adc_data_center/live_evaluation/data/evaluations/"